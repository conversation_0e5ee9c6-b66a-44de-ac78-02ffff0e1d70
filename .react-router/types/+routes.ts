// Generated by React Router

import "react-router"

declare module "react-router" {
  interface Register {
    pages: Pages
    routeFiles: RouteFiles
  }
}

type Pages = {
  "/": {
    params: {};
  };
  "/api/domains": {
    params: {};
  };
  "/api/scrape": {
    params: {};
  };
  "/domains/:date": {
    params: {
      "date": string;
    };
  };
  "/domains": {
    params: {};
  };
};

type RouteFiles = {
  "root.tsx": {
    id: "root";
    page: "/" | "/api/domains" | "/api/scrape" | "/domains/:date" | "/domains";
  };
  "routes/home.tsx": {
    id: "routes/home";
    page: "/";
  };
  "routes/api.domains.tsx": {
    id: "routes/api.domains";
    page: "/api/domains";
  };
  "routes/api.scrape.tsx": {
    id: "routes/api.scrape";
    page: "/api/scrape";
  };
  "routes/domains.tsx": {
    id: "routes/domains";
    page: "/domains/:date" | "/domains";
  };
};