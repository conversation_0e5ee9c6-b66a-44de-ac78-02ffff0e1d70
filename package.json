{"name": "domainsexpired", "version": "1.0.0", "description": "Track expired domains from Simply.com", "type": "module", "scripts": {"setup": "tsx scripts/setup.js", "dev": "react-router dev", "build": "react-router build", "start": "react-router-serve ./build/server/index.js", "typecheck": "tsc", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "scraper:run": "tsx app/services/scraper.ts", "scraper:test": "tsx scripts/test-scraper.js", "cron:start": "tsx app/services/cron.ts", "domain:create": "tsx scripts/create-domain.js", "logs:delete": "tsx scripts/delete-scraping-logs.js", "schema:migrate": "tsx scripts/migrate-schema.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:all": "tsx scripts/test-all.js", "db:check": "tsx scripts/check-database.js", "debug:scraper": "tsx scripts/debug-scraper.js", "db:cleanup": "tsx scripts/cleanup-duplicates.js"}, "dependencies": {"@prisma/client": "^5.7.1", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@react-router/node": "^7.6.1", "@react-router/serve": "^7.6.1", "@tailwindcss/typography": "^0.5.16", "axios": "^1.6.2", "cheerio": "^1.0.0-rc.12", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.0.6", "isbot": "^5", "lucide-react": "^0.511.0", "node-cron": "^3.0.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router": "^7.6.1", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8"}, "devDependencies": {"@react-router/dev": "^7.6.1", "@tailwindcss/postcss": "^4.1.8", "@types/jest": "^29.5.8", "@types/node": "^20.10.5", "@types/node-cron": "^3.0.11", "@types/react": "^18.2.45", "@types/react-dom": "^18.2.18", "@types/supertest": "^2.0.16", "autoprefixer": "^10.4.21", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "postcss": "^8.5.4", "prisma": "^5.7.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.2", "typescript": "^5.3.3", "vite": "^5.0.10"}, "engines": {"node": ">=18.0.0"}}