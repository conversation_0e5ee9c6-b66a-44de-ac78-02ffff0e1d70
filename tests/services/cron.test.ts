import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import cron from 'node-cron';
import { CronService } from '../../app/services/cron.js';

// Mock node-cron
jest.mock('node-cron');
const mockedCron = cron as jest.Mocked<typeof cron>;

// Mock DomainScraper
const mockScraper = {
  runScraping: jest.fn(),
};

jest.mock('~/services/scraper', () => ({
  DomainScraper: jest.fn().mockImplementation(() => mockScraper),
}));

describe('CronService', () => {
  let cronService: CronService;
  let originalEnv: NodeJS.ProcessEnv;

  beforeEach(() => {
    originalEnv = process.env;
    cronService = new CronService();
    jest.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
    jest.resetAllMocks();
  });

  describe('start', () => {
    it('should start cron service with default settings', () => {
      process.env.SCRAPING_ENABLED = 'true';
      process.env.SCRAPING_INTERVAL_HOURS = '1';

      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();

      expect(mockedCron.schedule).toHaveBeenCalledWith(
        '0 */1 * * *', // Every hour
        expect.any(Function),
        {
          scheduled: true,
          timezone: 'Europe/Copenhagen',
        }
      );
    });

    it('should start cron service with custom interval', () => {
      process.env.SCRAPING_ENABLED = 'true';
      process.env.SCRAPING_INTERVAL_HOURS = '6';

      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();

      expect(mockedCron.schedule).toHaveBeenCalledWith(
        '0 */6 * * *', // Every 6 hours
        expect.any(Function),
        {
          scheduled: true,
          timezone: 'Europe/Copenhagen',
        }
      );
    });

    it('should not start when scraping is disabled', () => {
      process.env.SCRAPING_ENABLED = 'false';

      cronService.start();

      expect(mockedCron.schedule).not.toHaveBeenCalled();
    });

    it('should not start when already running', () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();
      cronService.start(); // Second call

      expect(mockedCron.schedule).toHaveBeenCalledTimes(1);
    });

    it('should run initial scraping on start', async () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockScraper.runScraping.mockResolvedValue(undefined);
      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();

      // Wait for initial scraping to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(mockScraper.runScraping).toHaveBeenCalledTimes(1);
    });

    it('should handle initial scraping errors gracefully', async () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockScraper.runScraping.mockRejectedValue(new Error('Scraping failed'));
      mockedCron.schedule.mockReturnValue({} as any);

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      cronService.start();

      // Wait for initial scraping to complete
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(consoleSpy).toHaveBeenCalledWith('Initial scraping failed:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('stop', () => {
    it('should stop cron service when running', () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();
      cronService.stop();

      // Since node-cron doesn't have a destroy method, we just check the service state
      expect(cronService['isRunning']).toBe(false);
    });

    it('should not stop when not running', () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      cronService.stop();

      expect(consoleSpy).toHaveBeenCalledWith('Cron service is not running');
      consoleSpy.mockRestore();
    });
  });

  describe('scheduled task execution', () => {
    it('should execute scraping task when scheduled', async () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockScraper.runScraping.mockResolvedValue(undefined);

      let scheduledTask: Function;
      mockedCron.schedule.mockImplementation((expression, task) => {
        scheduledTask = task;
        return {} as any;
      });

      cronService.start();

      // Execute the scheduled task
      await scheduledTask!();

      expect(mockScraper.runScraping).toHaveBeenCalled();
    });

    it('should handle scheduled task errors', async () => {
      process.env.SCRAPING_ENABLED = 'true';
      mockScraper.runScraping.mockRejectedValue(new Error('Scheduled scraping failed'));

      let scheduledTask: Function;
      mockedCron.schedule.mockImplementation((expression, task) => {
        scheduledTask = task;
        return {} as any;
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      cronService.start();

      // Execute the scheduled task
      await scheduledTask!();

      expect(consoleSpy).toHaveBeenCalledWith('Scheduled scraping failed:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('environment variable handling', () => {
    it('should use default interval when not specified', () => {
      process.env.SCRAPING_ENABLED = 'true';
      delete process.env.SCRAPING_INTERVAL_HOURS;

      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();

      expect(mockedCron.schedule).toHaveBeenCalledWith(
        '0 */1 * * *', // Default 1 hour
        expect.any(Function),
        expect.any(Object)
      );
    });

    it('should handle invalid interval values', () => {
      process.env.SCRAPING_ENABLED = 'true';
      process.env.SCRAPING_INTERVAL_HOURS = 'invalid';

      mockedCron.schedule.mockReturnValue({} as any);

      cronService.start();

      expect(mockedCron.schedule).toHaveBeenCalledWith(
        '0 */NaN * * *', // Will result in NaN, but cron will handle it
        expect.any(Function),
        expect.any(Object)
      );
    });
  });
});
