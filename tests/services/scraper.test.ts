import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';
import axios from 'axios';
import { DomainScraper } from '../../app/services/scraper.js';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock Prisma
const mockPrisma = {
  domain: {
    upsert: jest.fn(),
    count: jest.fn(),
    findMany: jest.fn(),
  },
  scrapingLog: {
    create: jest.fn(),
    update: jest.fn(),
  },
};

jest.mock('~/lib/db.server', () => ({
  prisma: mockPrisma,
}));

describe('DomainScraper', () => {
  let scraper: DomainScraper;

  beforeEach(() => {
    scraper = new DomainScraper();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('scrapeExpiredDomains', () => {
    it('should successfully scrape domains from Simply.com', async () => {
      const mockHtml = `
        <div class="table-responsive">
          <table class="table table-xs table-hover table-striped">
            <tbody>
              <tr>
                <td>example.dk</td>
                <td>02.06.2025 06:52</td>
                <td>6 år</td>
              </tr>
              <tr>
                <td>test.dk</td>
                <td>15.12.2024 14:30</td>
                <td>2 år</td>
              </tr>
              <tr>
                <td>abc.dk</td>
                <td>01.01.2025 00:00</td>
                <td>1 år</td>
              </tr>
            </tbody>
          </table>
        </div>
      `;

      mockedAxios.get.mockResolvedValue({ data: mockHtml });

      const domains = await scraper.scrapeExpiredDomains();

      expect(domains).toHaveLength(3);
      expect(domains[0]).toEqual({
        domainName: 'example.dk',
        expirationDate: new Date('2025-06-02T06:52:00'),
        domainAge: '6 år',
      });
      expect(domains[1]).toEqual({
        domainName: 'test.dk',
        expirationDate: new Date('2024-12-15T14:30:00'),
        domainAge: '2 år',
      });
      expect(domains[2]).toEqual({
        domainName: 'abc.dk',
        expirationDate: new Date('2025-01-01T00:00:00'),
        domainAge: '1 år',
      });
    });

    it('should handle empty table', async () => {
      const mockHtml = `
        <div class="table-responsive">
          <table class="table table-xs table-hover table-striped">
            <tbody>
            </tbody>
          </table>
        </div>
      `;

      mockedAxios.get.mockResolvedValue({ data: mockHtml });

      const domains = await scraper.scrapeExpiredDomains();

      expect(domains).toHaveLength(0);
    });

    it('should handle missing table', async () => {
      const mockHtml = '<div>No table here</div>';

      mockedAxios.get.mockResolvedValue({ data: mockHtml });

      const domains = await scraper.scrapeExpiredDomains();

      expect(domains).toHaveLength(0);
    });

    it('should handle network errors', async () => {
      mockedAxios.get.mockRejectedValue(new Error('Network error'));

      await expect(scraper.scrapeExpiredDomains()).rejects.toThrow('Network error');
    });

    it('should handle invalid date formats', async () => {
      const mockHtml = `
        <div class="table-responsive">
          <table class="table table-xs table-hover table-striped">
            <tbody>
              <tr>
                <td>example.dk</td>
                <td>invalid-date</td>
                <td>6 år</td>
              </tr>
              <tr>
                <td>test.dk</td>
                <td>15.12.2024 14:30</td>
                <td>2 år</td>
              </tr>
            </tbody>
          </table>
        </div>
      `;

      mockedAxios.get.mockResolvedValue({ data: mockHtml });

      const domains = await scraper.scrapeExpiredDomains();

      // Should only return the domain with valid date
      expect(domains).toHaveLength(1);
      expect(domains[0].domainName).toBe('test.dk');
    });
  });

  describe('saveDomains', () => {
    it('should save domains to database successfully', async () => {
      const mockDomains = [
        {
          domainName: 'abc.dk',
          expirationDate: new Date('2025-01-01T00:00:00'),
          domainAge: '1 år',
        },
        {
          domainName: 'example.dk',
          expirationDate: new Date('2025-06-02T06:52:00'),
          domainAge: '6 år',
        },
      ];

      const mockLogEntry = { id: 'log-123' };
      mockPrisma.scrapingLog.create.mockResolvedValue(mockLogEntry);
      mockPrisma.domain.upsert.mockResolvedValue({});
      mockPrisma.scrapingLog.update.mockResolvedValue({});

      await scraper.saveDomains(mockDomains);

      expect(mockPrisma.scrapingLog.create).toHaveBeenCalledWith({
        data: {
          status: 'running',
          domainsFound: 2,
        },
      });

      expect(mockPrisma.domain.upsert).toHaveBeenCalledTimes(2);
      
      // Check first domain (abc.dk should be highlighted due to 3 chars)
      expect(mockPrisma.domain.upsert).toHaveBeenCalledWith({
        where: {
          domainName_collectionDate: {
            domainName: 'abc.dk',
            collectionDate: expect.any(Date),
          },
        },
        update: {
          expirationDate: mockDomains[0].expirationDate,
          domainAge: '1 år',
          domainRating: 100, // 3 chars = 100 points + 20 (no numbers) + 10 (no hyphens)
          isHighlighted: true,
        },
        create: {
          domainName: 'abc.dk',
          expirationDate: mockDomains[0].expirationDate,
          domainAge: '1 år',
          collectionDate: expect.any(Date),
          domainRating: 100,
          isHighlighted: true,
        },
      });

      expect(mockPrisma.scrapingLog.update).toHaveBeenCalledWith({
        where: { id: 'log-123' },
        data: {
          status: 'completed',
          endTime: expect.any(Date),
        },
      });
    });

    it('should handle database errors', async () => {
      const mockDomains = [
        {
          domainName: 'test.dk',
          expirationDate: new Date('2025-01-01T00:00:00'),
          domainAge: '1 år',
        },
      ];

      const mockLogEntry = { id: 'log-123' };
      mockPrisma.scrapingLog.create.mockResolvedValue(mockLogEntry);
      mockPrisma.domain.upsert.mockRejectedValue(new Error('Database error'));

      await expect(scraper.saveDomains(mockDomains)).rejects.toThrow('Database error');

      expect(mockPrisma.scrapingLog.update).toHaveBeenCalledWith({
        where: { id: 'log-123' },
        data: {
          status: 'failed',
          endTime: expect.any(Date),
          errorMessage: 'Database error',
        },
      });
    });
  });

  describe('runScraping', () => {
    it('should run complete scraping process', async () => {
      const mockHtml = `
        <div class="table-responsive">
          <table class="table table-xs table-hover table-striped">
            <tbody>
              <tr>
                <td>test.dk</td>
                <td>15.12.2024 14:30</td>
                <td>2 år</td>
              </tr>
            </tbody>
          </table>
        </div>
      `;

      mockedAxios.get.mockResolvedValue({ data: mockHtml });
      
      const mockLogEntry = { id: 'log-123' };
      mockPrisma.scrapingLog.create.mockResolvedValue(mockLogEntry);
      mockPrisma.domain.upsert.mockResolvedValue({});
      mockPrisma.scrapingLog.update.mockResolvedValue({});

      await scraper.runScraping();

      expect(mockedAxios.get).toHaveBeenCalledWith(
        'https://www.simply.com/dk/ninja/',
        expect.objectContaining({
          headers: expect.objectContaining({
            'User-Agent': expect.stringContaining('Mozilla'),
          }),
          timeout: 30000,
        })
      );

      expect(mockPrisma.scrapingLog.create).toHaveBeenCalled();
      expect(mockPrisma.domain.upsert).toHaveBeenCalled();
      expect(mockPrisma.scrapingLog.update).toHaveBeenCalledWith({
        where: { id: 'log-123' },
        data: {
          status: 'completed',
          endTime: expect.any(Date),
        },
      });
    });
  });

  describe('domain rating calculation', () => {
    it('should calculate correct ratings for different domain types', async () => {
      const testCases = [
        { domain: 'a.dk', expectedRating: 130 }, // 3 chars: 100 + no numbers: 20 + no hyphens: 10
        { domain: 'ab.dk', expectedRating: 130 }, // 2 chars: 100 + 20 + 10
        { domain: 'abc.dk', expectedRating: 130 }, // 3 chars: 100 + 20 + 10
        { domain: 'abcd.dk', expectedRating: 80 }, // 4 chars: 50 + 20 + 10
        { domain: 'abcde.dk', expectedRating: 80 }, // 5 chars: 50 + 20 + 10
        { domain: 'abcdef.dk', expectedRating: 55 }, // 6 chars: 25 + 20 + 10
        { domain: 'abc123.dk', expectedRating: 110 }, // 6 chars: 25 + no hyphens: 10, but has numbers: 0
        { domain: 'abc-def.dk', expectedRating: 45 }, // 7 chars: 25 + no numbers: 20, but has hyphen: 0
        { domain: 'verylongdomain.dk', expectedRating: 20 }, // >8 chars: 0 + no numbers: 20 + no hyphens: 10
      ];

      for (const testCase of testCases) {
        const mockHtml = `
          <div class="table-responsive">
            <table class="table table-xs table-hover table-striped">
              <tbody>
                <tr>
                  <td>${testCase.domain}</td>
                  <td>15.12.2024 14:30</td>
                  <td>2 år</td>
                </tr>
              </tbody>
            </table>
          </div>
        `;

        mockedAxios.get.mockResolvedValue({ data: mockHtml });
        
        const mockLogEntry = { id: 'log-123' };
        mockPrisma.scrapingLog.create.mockResolvedValue(mockLogEntry);
        mockPrisma.domain.upsert.mockResolvedValue({});
        mockPrisma.scrapingLog.update.mockResolvedValue({});

        await scraper.runScraping();

        expect(mockPrisma.domain.upsert).toHaveBeenCalledWith(
          expect.objectContaining({
            create: expect.objectContaining({
              domainRating: testCase.expectedRating,
              isHighlighted: testCase.domain.length <= 3,
            }),
          })
        );

        jest.clearAllMocks();
      }
    });
  });
});
