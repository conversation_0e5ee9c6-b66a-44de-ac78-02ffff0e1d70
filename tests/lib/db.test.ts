import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock PrismaClient
const mockPrismaClient = {
  $connect: jest.fn(),
  $disconnect: jest.fn(),
  domain: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    count: jest.fn(),
  },
  scrapingLog: {
    findMany: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
  },
};

jest.mock('@prisma/client', () => ({
  PrismaClient: jest.fn().mockImplementation(() => mockPrismaClient),
}));

describe('Database Connection', () => {
  let originalEnv: NodeJS.ProcessEnv;
  let originalGlobal: any;

  beforeEach(() => {
    originalEnv = process.env;
    originalGlobal = (global as any).__db__;
    delete (global as any).__db__;
    jest.clearAllMocks();
  });

  afterEach(() => {
    process.env = originalEnv;
    (global as any).__db__ = originalGlobal;
    jest.resetModules();
  });

  it('should create a new Prisma client in production', async () => {
    process.env.NODE_ENV = 'production';

    // Import the module after setting environment
    const { prisma } = await import('~/lib/db.server');

    expect(prisma).toBeDefined();
  });

  it('should reuse global Prisma client in development', async () => {
    process.env.NODE_ENV = 'development';

    // First import
    const { prisma: prisma1 } = await import('~/lib/db.server');

    // Clear module cache and import again
    jest.resetModules();
    const { prisma: prisma2 } = await import('~/lib/db.server');

    expect(prisma1).toBeDefined();
    expect(prisma2).toBeDefined();
  });

  it('should connect to database in development', async () => {
    process.env.NODE_ENV = 'development';

    await import('~/lib/db.server');

    expect(mockPrismaClient.$connect).toHaveBeenCalled();
  });

  it('should handle database connection errors', async () => {
    process.env.NODE_ENV = 'development';

    // Create a mock that rejects on connect
    const failingMockClient = {
      ...mockPrismaClient,
      $connect: jest.fn().mockRejectedValue(new Error('Connection failed')),
    };

    // Mock the PrismaClient constructor to return our failing mock
    const { PrismaClient } = await import('@prisma/client');
    (PrismaClient as jest.MockedClass<typeof PrismaClient>).mockImplementation(() => failingMockClient as any);

    // Should not throw, but may log error
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    try {
      await import('~/lib/db.server');
      // Connection errors should be handled gracefully
    } catch (error) {
      // This is expected behavior
    }

    consoleSpy.mockRestore();
  });
});
