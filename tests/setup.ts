import { beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';

// Mock environment variables
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/test_db?schema=public';
process.env.NODE_ENV = 'test';
process.env.SCRAPING_ENABLED = 'false';
process.env.SIMPLY_URL = 'https://www.simply.com/dk/ninja/';

// Global test setup
beforeAll(async () => {
  // Setup code that runs once before all tests
});

afterAll(async () => {
  // Cleanup code that runs once after all tests
});

beforeEach(async () => {
  // Setup code that runs before each test
});

afterEach(async () => {
  // Cleanup code that runs after each test
});
