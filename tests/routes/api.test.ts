import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock Prisma
const mockPrisma = {
  domain: {
    findMany: jest.fn(),
    count: jest.fn(),
    upsert: jest.fn(),
  },
  scrapingLog: {
    create: jest.fn(),
    update: jest.fn(),
  },
};

jest.mock('~/lib/db.server', () => ({
  prisma: mockPrisma,
}));

// Mock DomainScraper
const mockScraper = {
  runScraping: jest.fn(),
};

jest.mock('~/services/scraper', () => ({
  DomainScraper: jest.fn().mockImplementation(() => mockScraper),
}));

describe('API Routes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('/api/domains loader', () => {
    it('should return domains with pagination', async () => {
      const mockDomains = [
        {
          id: '1',
          domainName: 'example.dk',
          expirationDate: new Date('2025-01-01'),
          domainAge: '5 år',
          isHighlighted: false,
          domainRating: 50,
        },
        {
          id: '2',
          domainName: 'abc.dk',
          expirationDate: new Date('2025-01-02'),
          domainAge: '2 år',
          isHighlighted: true,
          domainRating: 130,
        },
      ];

      mockPrisma.domain.findMany.mockResolvedValue(mockDomains);
      mockPrisma.domain.count.mockResolvedValue(2);

      const { loader } = await import('~/routes/api.domains');
      
      const request = new Request('http://localhost/api/domains?page=1&limit=50');
      const response = await loader({ request, params: {}, context: {} });
      
      expect(response).toBeInstanceOf(Response);
      
      const data = await response.json();
      expect(data.domains).toEqual(mockDomains);
      expect(data.pagination).toEqual({
        page: 1,
        limit: 50,
        total: 2,
        totalPages: 1,
      });
    });

    it('should filter domains by date', async () => {
      const mockDomains = [
        {
          id: '1',
          domainName: 'example.dk',
          expirationDate: new Date('2025-01-01'),
          domainAge: '5 år',
          isHighlighted: false,
          domainRating: 50,
        },
      ];

      mockPrisma.domain.findMany.mockResolvedValue(mockDomains);
      mockPrisma.domain.count.mockResolvedValue(1);

      const { loader } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains?date=2024-12-01');
      const response = await loader({ request, params: {}, context: {} });
      
      expect(mockPrisma.domain.findMany).toHaveBeenCalledWith({
        where: {
          collectionDate: {
            gte: expect.any(Date),
            lte: expect.any(Date),
          },
        },
        orderBy: [
          { isHighlighted: 'desc' },
          { domainRating: 'desc' },
          { domainName: 'asc' },
        ],
        skip: 0,
        take: 50,
      });
    });

    it('should filter domains by search term', async () => {
      mockPrisma.domain.findMany.mockResolvedValue([]);
      mockPrisma.domain.count.mockResolvedValue(0);

      const { loader } = await import('~/routes/api.domains');
      
      const request = new Request('http://localhost/api/domains?search=example');
      await loader({ request, params: {}, context: {} });
      
      expect(mockPrisma.domain.findMany).toHaveBeenCalledWith({
        where: {
          domainName: {
            contains: 'example',
            mode: 'insensitive',
          },
        },
        orderBy: [
          { isHighlighted: 'desc' },
          { domainRating: 'desc' },
          { domainName: 'asc' },
        ],
        skip: 0,
        take: 50,
      });
    });

    it('should filter highlighted domains only', async () => {
      mockPrisma.domain.findMany.mockResolvedValue([]);
      mockPrisma.domain.count.mockResolvedValue(0);

      const { loader } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains?highlighted=true');
      await loader({ request, params: {}, context: {} });
      
      expect(mockPrisma.domain.findMany).toHaveBeenCalledWith({
        where: {
          isHighlighted: true,
        },
        orderBy: [
          { isHighlighted: 'desc' },
          { domainRating: 'desc' },
          { domainName: 'asc' },
        ],
        skip: 0,
        take: 50,
      });
    });

    it('should handle database errors', async () => {
      mockPrisma.domain.findMany.mockRejectedValue(new Error('Database error'));

      const { loader } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains');
      const response = await loader({ request, params: {}, context: {} });

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Failed to fetch domains');
    });
  });

  describe('/api/domains action', () => {
    it('should trigger manual scraping on POST', async () => {
      mockScraper.runScraping.mockResolvedValue(undefined);

      const { action } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains', {
        method: 'POST',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.message).toBe('Scraping completed successfully');
      expect(mockScraper.runScraping).toHaveBeenCalled();
    });

    it('should handle scraping errors', async () => {
      mockScraper.runScraping.mockRejectedValue(new Error('Scraping failed'));

      const { action } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains', {
        method: 'POST',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Scraping failed');
    });

    it('should reject non-POST methods', async () => {
      const { action } = await import('~/routes/api.domains');

      const request = new Request('http://localhost/api/domains', {
        method: 'GET',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data.error).toBe('Method not allowed');
    });
  });

  describe('/api/scrape', () => {
    it('should trigger scraping on POST', async () => {
      mockScraper.runScraping.mockResolvedValue(undefined);

      const { action } = await import('~/routes/api.scrape');

      const request = new Request('http://localhost/api/scrape', {
        method: 'POST',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(200);
      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.message).toBe('Domain scraping completed successfully');
    });

    it('should handle scraping errors with details', async () => {
      mockScraper.runScraping.mockRejectedValue(new Error('Network timeout'));

      const { action } = await import('~/routes/api.scrape');

      const request = new Request('http://localhost/api/scrape', {
        method: 'POST',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(500);
      const data = await response.json();
      expect(data.error).toBe('Scraping failed');
      expect(data.details).toBe('Network timeout');
    });

    it('should reject GET requests', async () => {
      const { loader } = await import('~/routes/api.scrape');

      const response = await loader({ request: new Request('http://localhost/api/scrape'), params: {}, context: {} });

      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data.error).toBe('GET method not allowed');
    });

    it('should reject non-POST methods', async () => {
      const { action } = await import('~/routes/api.scrape');

      const request = new Request('http://localhost/api/scrape', {
        method: 'PUT',
      });
      const response = await action({ request, params: {}, context: {} });

      expect(response.status).toBe(405);
      const data = await response.json();
      expect(data.error).toBe('Method not allowed');
    });
  });
});
