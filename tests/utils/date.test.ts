import { describe, it, expect } from '@jest/globals';
import { format, parse, startOfDay, endOfDay } from 'date-fns';

describe('Date Utilities', () => {
  describe('date parsing', () => {
    it('should parse Simply.com date format correctly', () => {
      const dateString = '02.06.2025 06:52';
      const parsed = parse(dateString, 'dd.MM.yyyy HH:mm', new Date());
      
      expect(parsed.getFullYear()).toBe(2025);
      expect(parsed.getMonth()).toBe(5); // June (0-indexed)
      expect(parsed.getDate()).toBe(2);
      expect(parsed.getHours()).toBe(6);
      expect(parsed.getMinutes()).toBe(52);
    });

    it('should handle different date formats', () => {
      const testCases = [
        { input: '01.01.2025 00:00', expected: new Date(2025, 0, 1, 0, 0) },
        { input: '31.12.2024 23:59', expected: new Date(2024, 11, 31, 23, 59) },
        { input: '15.06.2025 12:30', expected: new Date(2025, 5, 15, 12, 30) },
      ];

      testCases.forEach(({ input, expected }) => {
        const parsed = parse(input, 'dd.MM.yyyy HH:mm', new Date());
        expect(parsed.getTime()).toBe(expected.getTime());
      });
    });

    it('should handle invalid date strings', () => {
      const invalidDates = [
        'invalid-date',
        '32.01.2025 00:00', // Invalid day
        '01.13.2025 00:00', // Invalid month
        '01.01.2025 25:00', // Invalid hour
      ];

      invalidDates.forEach(dateString => {
        const parsed = parse(dateString, 'dd.MM.yyyy HH:mm', new Date());
        expect(isNaN(parsed.getTime())).toBe(true);
      });
    });
  });

  describe('date formatting', () => {
    it('should format dates for display', () => {
      const date = new Date(2025, 5, 2, 6, 52); // June 2, 2025 06:52
      
      const formatted = format(date, 'MMM dd, yyyy HH:mm');
      expect(formatted).toBe('Jun 02, 2025 06:52');
    });

    it('should format dates for URL parameters', () => {
      const date = new Date(2025, 5, 2);
      
      const formatted = format(date, 'yyyy-MM-dd');
      expect(formatted).toBe('2025-06-02');
    });
  });

  describe('date range utilities', () => {
    it('should create start and end of day correctly', () => {
      const date = new Date(2025, 5, 2, 14, 30, 45); // June 2, 2025 14:30:45
      
      const start = startOfDay(date);
      const end = endOfDay(date);
      
      expect(start.getHours()).toBe(0);
      expect(start.getMinutes()).toBe(0);
      expect(start.getSeconds()).toBe(0);
      expect(start.getMilliseconds()).toBe(0);
      
      expect(end.getHours()).toBe(23);
      expect(end.getMinutes()).toBe(59);
      expect(end.getSeconds()).toBe(59);
      expect(end.getMilliseconds()).toBe(999);
    });

    it('should maintain date when creating ranges', () => {
      const date = new Date(2025, 5, 2, 14, 30, 45);
      
      const start = startOfDay(date);
      const end = endOfDay(date);
      
      expect(start.getFullYear()).toBe(2025);
      expect(start.getMonth()).toBe(5);
      expect(start.getDate()).toBe(2);
      
      expect(end.getFullYear()).toBe(2025);
      expect(end.getMonth()).toBe(5);
      expect(end.getDate()).toBe(2);
    });
  });

  describe('timezone handling', () => {
    it('should handle dates consistently', () => {
      const dateString = '02.06.2025 06:52';
      const parsed = parse(dateString, 'dd.MM.yyyy HH:mm', new Date());
      
      // Should parse in local timezone
      expect(parsed.getTimezoneOffset()).toBe(new Date().getTimezoneOffset());
    });
  });
});
