// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Domain {
  id                String   @id @default(cuid())
  domainName        String
  expirationDate    DateTime
  domainAge         String
  collectionDate    DateTime @default(now())
  domainRating      Int?     @default(0)
  isHighlighted     Boolean  @default(false)
  createdAt         DateTime @default(now())
  updatedAt         DateTime @updatedAt

  @@unique([domainName, collectionDate])
  @@index([collectionDate])
  @@index([domainName])
  @@index([isHighlighted])
  @@map("domains")
}

model ScrapingLog {
  id          String   @id @default(cuid())
  startTime   DateTime @default(now())
  endTime     DateTime?
  status      String   // 'running', 'completed', 'failed'
  domainsFound Int?
  errorMessage String?
  createdAt   DateTime @default(now())

  @@map("scraping_logs")
}
