# <PERSON><PERSON> vs Prisma Upsert Comparison

## 🔄 **UPSERT CONCEPT**
Upsert = **UP**date or in**SERT** - Insert a record if it doesn't exist, update if it does.

---

## 🟡 **LARAVEL UPSERT**

### Basic Laravel Upsert Syntax
```php
// Laravel Eloquent upsert
DB::table('domains')->upsert(
    [
        ['domain_name' => 'example.dk', 'expiration_date' => '2025-06-02', 'domain_age' => '5 år'],
        ['domain_name' => 'test.dk', 'expiration_date' => '2025-06-02', 'domain_age' => '3 år'],
    ],
    ['domain_name', 'collection_date'], // Unique columns
    ['expiration_date', 'domain_age', 'domain_rating'] // Columns to update
);
```

### Laravel Model Upsert
```php
// Using Eloquent Model
Domain::upsert(
    $domainsData,
    ['domain_name', 'collection_date'], // Unique constraint
    ['expiration_date', 'domain_age', 'domain_rating', 'updated_at'] // Update columns
);
```

### Laravel updateOrCreate (Single Record)
```php
// For single records
Domain::updateOrCreate(
    ['domain_name' => 'example.dk', 'collection_date' => '2025-06-02'], // Where conditions
    ['expiration_date' => '2025-06-02', 'domain_age' => '5 år'] // Data to update/create
);
```

---

## 🔵 **PRISMA UPSERT**

### Basic Prisma Upsert Syntax
```typescript
// Prisma upsert (single record)
const result = await prisma.domain.upsert({
  where: {
    // Unique constraint identifier
    domainName_collectionDate: {
      domainName: 'example.dk',
      collectionDate: new Date('2025-06-02')
    }
  },
  update: {
    // Data to update if record exists
    expirationDate: new Date('2025-06-02T06:52:00Z'),
    domainAge: '5 år',
    domainRating: 80,
    updatedAt: new Date()
  },
  create: {
    // Data to create if record doesn't exist
    domainName: 'example.dk',
    expirationDate: new Date('2025-06-02T06:52:00Z'),
    domainAge: '5 år',
    collectionDate: new Date('2025-06-02'),
    domainRating: 80,
    isHighlighted: false
  }
});
```

### Prisma Bulk Upsert (Multiple Records)
```typescript
// Prisma doesn't have native bulk upsert, so we loop
const results = [];
for (const domain of domains) {
  const result = await prisma.domain.upsert({
    where: {
      domainName_collectionDate: {
        domainName: domain.domainName,
        collectionDate: normalizedDate
      }
    },
    update: {
      expirationDate: domain.expirationDate,
      domainAge: domain.domainAge,
      domainRating: calculateRating(domain.domainName),
      updatedAt: new Date()
    },
    create: {
      domainName: domain.domainName,
      expirationDate: domain.expirationDate,
      domainAge: domain.domainAge,
      collectionDate: normalizedDate,
      domainRating: calculateRating(domain.domainName),
      isHighlighted: domain.domainName.length <= 3
    }
  });
  results.push(result);
}
```

### Prisma Transaction for Bulk Operations
```typescript
// Using transaction for better performance
const results = await prisma.$transaction(
  domains.map(domain => 
    prisma.domain.upsert({
      where: {
        domainName_collectionDate: {
          domainName: domain.domainName,
          collectionDate: normalizedDate
        }
      },
      update: {
        expirationDate: domain.expirationDate,
        domainAge: domain.domainAge,
        domainRating: calculateRating(domain.domainName),
        updatedAt: new Date()
      },
      create: {
        domainName: domain.domainName,
        expirationDate: domain.expirationDate,
        domainAge: domain.domainAge,
        collectionDate: normalizedDate,
        domainRating: calculateRating(domain.domainName),
        isHighlighted: domain.domainName.length <= 3
      }
    })
  )
);
```

---

## 📊 **COMPARISON TABLE**

| Feature | Laravel | Prisma |
|---------|---------|---------|
| **Bulk Upsert** | ✅ Native support | ❌ Loop required |
| **Single Upsert** | ✅ `updateOrCreate()` | ✅ `upsert()` |
| **Performance** | 🚀 Fast (bulk) | 🐌 Slower (individual) |
| **Type Safety** | ❌ No types | ✅ Full TypeScript |
| **Syntax** | 🟡 Array-based | 🟢 Object-based |
| **Transactions** | ✅ Built-in | ✅ `$transaction()` |
| **Return Values** | 📊 Affected rows | 📦 Full objects |

---

## 🎯 **BEST PRACTICES**

### Laravel Best Practices
```php
// Use upsert for bulk operations
Domain::upsert($bulkData, $uniqueColumns, $updateColumns);

// Use updateOrCreate for single records
Domain::updateOrCreate($whereConditions, $data);

// Use transactions for complex operations
DB::transaction(function () {
    // Multiple operations
});
```

### Prisma Best Practices
```typescript
// Use upsert for single records
await prisma.domain.upsert({ where, update, create });

// Use transactions for bulk operations
await prisma.$transaction(upsertOperations);

// Use createMany for insert-only bulk operations
await prisma.domain.createMany({ data: domains, skipDuplicates: true });
```

---

## 🔧 **OUR IMPLEMENTATION**

In our domain scraper, we use Prisma upsert with the composite unique constraint:

```typescript
// Prisma schema
model Domain {
  id             String   @id @default(cuid())
  domainName     String
  collectionDate DateTime
  // ... other fields
  
  @@unique([domainName, collectionDate], name: "domainName_collectionDate")
}

// Usage in scraper
await prisma.domain.upsert({
  where: {
    domainName_collectionDate: {
      domainName: domain.domainName,
      collectionDate: normalizedCollectionDate
    }
  },
  update: {
    expirationDate: domain.expirationDate,
    domainAge: domain.domainAge,
    domainRating: rating,
    isHighlighted,
    updatedAt: new Date()
  },
  create: {
    domainName: domain.domainName,
    expirationDate: domain.expirationDate,
    domainAge: domain.domainAge,
    collectionDate: normalizedCollectionDate,
    domainRating: rating,
    isHighlighted
  }
});
```

This ensures:
- ✅ No duplicates for same domain on same day
- ✅ Updates existing records with fresh data
- ✅ Creates new records when needed
- ✅ Type-safe operations
- ✅ Proper error handling
