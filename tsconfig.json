{"include": ["**/*.ts", "**/*.tsx"], "compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES6"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "module": "ESNext", "target": "ES2022", "types": ["vite/client"], "paths": {"~/*": ["./app/*"]}}}