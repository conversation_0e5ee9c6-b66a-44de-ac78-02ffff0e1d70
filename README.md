# Domains Expired Tracker

A React Router 7 application that tracks expired domains from Simply.com with automated data collection and PostgreSQL storage.

## Features

- **Automated Data Collection**: Hourly scraping of expired domains from Simply.com
- **PostgreSQL Database**: Persistent storage with Prisma ORM
- **Domain Highlighting**: Prioritizes domains with 3 characters or less
- **Historical Data**: View domains from any previous collection date
- **Search & Filter**: Find specific domains and filter by highlighted status
- **Responsive Design**: Works on desktop and mobile devices
- **Real-time Updates**: Manual scraping trigger and activity monitoring

## Technology Stack

- **Frontend**: React Router 7 (Framework Mode)
- **Backend**: Node.js with React Router server functions
- **Database**: PostgreSQL with Prisma ORM
- **Scraping**: Cheer<PERSON> for HTML parsing, Axios for HTTP requests
- **Scheduling**: Node-cron for automated data collection
- **Styling**: Tailwind CSS (via CDN)

## Prerequisites

- Node.js 18+ 
- PostgreSQL database
- npm or yarn package manager

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd domainsexpired
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit `.env` and configure your PostgreSQL connection:
   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/domainsexpired?schema=public"
   ```

4. **Set up the database**
   ```bash
   npm run db:push
   npm run db:generate
   ```

## Usage

### Development

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Start the cron service (in a separate terminal)**
   ```bash
   npm run cron:start
   ```

3. **Manual scraping (optional)**
   ```bash
   npm run scraper:run
   ```

### Production

1. **Build the application**
   ```bash
   npm run build
   ```

2. **Start the production server**
   ```bash
   npm start
   ```

## Database Schema

### Domains Table
- `id`: Unique identifier
- `domainName`: The domain name (e.g., "example.dk")
- `expirationDate`: When the domain expires
- `domainAge`: Age of the domain (e.g., "6 år")
- `collectionDate`: When this data was collected
- `domainRating`: Calculated rating based on domain characteristics
- `isHighlighted`: Whether the domain should be highlighted (≤3 characters)

## Single Page Application

The application uses a single-page architecture with React Router 7:
- All functionality is available on the home page
- No separate API endpoints - uses React Router actions
- Server-side filtering and pagination via URL parameters
- Real-time updates without page reloads

## Configuration

### Environment Variables

- `DATABASE_URL`: PostgreSQL connection string
- `SCRAPING_ENABLED`: Enable/disable automated scraping (true/false)
- `SCRAPING_INTERVAL_HOURS`: Hours between scraping runs (default: 1)
- `SIMPLY_URL`: URL to scrape (default: https://www.simply.com/dk/ninja/)

### Domain Rating System

Domains are automatically rated based on:
- Length (shorter = higher rating)
- Character composition (no numbers/hyphens = bonus points)
- Domains ≤3 characters are automatically highlighted

## Monitoring

- View scraping activity on the home page
- Check recent logs and success/failure status
- Monitor domain collection statistics

## Troubleshooting

### Common Issues

1. **Database Connection Errors**
   - Verify PostgreSQL is running
   - Check DATABASE_URL in .env file
   - Ensure database exists and user has proper permissions

2. **Scraping Failures**
   - Check internet connection
   - Verify Simply.com website structure hasn't changed
   - Review error logs in the scraping logs table

3. **Build Errors**
   - Clear node_modules and reinstall: `rm -rf node_modules && npm install`
   - Check Node.js version compatibility

## Development

### Project Structure
```
app/
├── lib/
│   └── db.server.ts          # Database connection
├── routes/
│   ├── home.tsx              # Home page
│   ├── domains.tsx           # Domain listing page
│   ├── api.domains.tsx       # Domains API
│   └── api.scrape.tsx        # Scraping API
├── services/
│   ├── scraper.ts            # Web scraping logic
│   └── cron.ts               # Cron job management
├── root.tsx                  # App root component
└── routes.ts                 # Route configuration
```

### Adding New Features

1. **Database Changes**: Update `prisma/schema.prisma` and run `npm run db:push`
2. **New Routes**: Add to `app/routes.ts` and create corresponding files
3. **Scraping Logic**: Modify `app/services/scraper.ts`
4. **Styling**: Update component classes (currently using Tailwind via CDN)

## License

MIT License - see LICENSE file for details
