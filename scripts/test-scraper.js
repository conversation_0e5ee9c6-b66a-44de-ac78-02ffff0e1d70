import { DomainScraper } from '../app/services/scraper.js';

async function testScraper() {
  console.log('Testing domain scraper...');
  
  try {
    const scraper = new DomainScraper();
    
    // Test scraping without saving to database
    console.log('Fetching domains from Simply.com...');
    const domains = await scraper.scrapeExpiredDomains();
    
    console.log(`\nFound ${domains.length} domains:`);
    
    // Show first 5 domains as example
    domains.slice(0, 5).forEach((domain, index) => {
      console.log(`${index + 1}. ${domain.domainName}`);
      console.log(`   Expires: ${domain.expirationDate.toISOString()}`);
      console.log(`   Age: ${domain.domainAge}`);
      console.log('');
    });
    
    if (domains.length > 5) {
      console.log(`... and ${domains.length - 5} more domains`);
    }
    
    console.log('✅ Scraper test completed successfully!');
    
  } catch (error) {
    console.error('❌ Scraper test failed:', error.message);
    process.exit(1);
  }
}

testScraper();
