import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function cleanupDuplicates() {
  console.log('🧹 Starting duplicate cleanup...\n');

  try {
    // Check current state
    console.log('📊 Current database state:');
    const totalDomains = await prisma.domain.count();
    console.log(`   Total domains: ${totalDomains}`);

    // Group by collection date to see duplicates
    const domainsByDate = await prisma.domain.groupBy({
      by: ['collectionDate'],
      _count: { id: true },
      orderBy: { collectionDate: 'desc' }
    });

    console.log('\n📅 Domains by collection date:');
    domainsByDate.forEach(group => {
      console.log(`   ${group.collectionDate.toLocaleDateString()}: ${group._count.id} domains`);
    });

    // Find duplicates (same domain name on same collection date)
    console.log('\n🔍 Checking for duplicates...');
    
    const duplicates = await prisma.$queryRaw`
      SELECT "domainName", "collectionDate", COUNT(*) as count
      FROM "domains"
      GROUP BY "domainName", "collectionDate"
      HAVING COUNT(*) > 1
      ORDER BY count DESC
      LIMIT 10
    `;

    if (duplicates.length > 0) {
      console.log(`❌ Found ${duplicates.length} sets of duplicates:`);
      duplicates.forEach((dup, i) => {
        console.log(`   ${i + 1}. ${dup.domainName} on ${dup.collectionDate.toLocaleDateString()}: ${dup.count} copies`);
      });

      // Ask user if they want to clean up
      console.log('\n🧹 Cleaning up duplicates...');
      
      // For each duplicate set, keep only the most recent one
      for (const dup of duplicates) {
        const domainsToDelete = await prisma.domain.findMany({
          where: {
            domainName: dup.domainName,
            collectionDate: dup.collectionDate
          },
          orderBy: { createdAt: 'desc' },
          skip: 1 // Keep the first (most recent) one
        });

        if (domainsToDelete.length > 0) {
          const deleteResult = await prisma.domain.deleteMany({
            where: {
              id: {
                in: domainsToDelete.map(d => d.id)
              }
            }
          });
          
          console.log(`   🗑️ Deleted ${deleteResult.count} duplicate entries for ${dup.domainName}`);
        }
      }
    } else {
      console.log('✅ No duplicates found!');
    }

    // Check if we have multiple entries for today
    const today = new Date();
    const todayStart = new Date(today.toDateString());
    const todayEnd = new Date(todayStart.getTime() + 24 * 60 * 60 * 1000);

    const todayDomains = await prisma.domain.count({
      where: {
        collectionDate: {
          gte: todayStart,
          lt: todayEnd
        }
      }
    });

    console.log(`\n📊 Domains for today (${today.toLocaleDateString()}): ${todayDomains}`);

    // Check for multiple collection dates today
    const todayCollectionDates = await prisma.domain.groupBy({
      by: ['collectionDate'],
      _count: { id: true },
      where: {
        collectionDate: {
          gte: todayStart,
          lt: todayEnd
        }
      }
    });

    if (todayCollectionDates.length > 1) {
      console.log(`⚠️ Found ${todayCollectionDates.length} different collection times for today:`);
      todayCollectionDates.forEach(group => {
        console.log(`   ${group.collectionDate.toISOString()}: ${group._count.id} domains`);
      });
    }

    // Final state
    console.log('\n📊 Final database state:');
    const finalTotal = await prisma.domain.count();
    console.log(`   Total domains: ${finalTotal}`);
    
    const finalByDate = await prisma.domain.groupBy({
      by: ['collectionDate'],
      _count: { id: true },
      orderBy: { collectionDate: 'desc' },
      take: 5
    });

    console.log('\n📅 Recent collection dates:');
    finalByDate.forEach(group => {
      console.log(`   ${group.collectionDate.toLocaleDateString()}: ${group._count.id} domains`);
    });

  } catch (error) {
    console.error('❌ Cleanup failed:', error);
  } finally {
    await prisma.$disconnect();
  }
}

cleanupDuplicates()
  .then(() => {
    console.log('\n✅ Cleanup completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Cleanup failed:', error);
    process.exit(1);
  });
