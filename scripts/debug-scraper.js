import { DomainScraper } from '../app/services/scraper.js';

console.log('🔧 DEBUG: Starting scraper debug session...');
console.log('🔧 DEBUG: Node version:', process.version);
console.log('🔧 DEBUG: Working directory:', process.cwd());
console.log('🔧 DEBUG: Environment variables:');
console.log('   NODE_ENV:', process.env.NODE_ENV);
console.log('   DATABASE_URL:', process.env.DATABASE_URL ? 'configured' : 'missing');
console.log('   SIMPLY_URL:', process.env.SIMPLY_URL);

async function debugScraper() {
  try {
    console.log('\n🔧 DEBUG: Creating DomainScraper instance...');
    const scraper = new DomainScraper();
    
    console.log('🔧 DEBUG: Starting scraping process...');
    await scraper.runScraping();
    
    console.log('🔧 DEBUG: Scraping completed successfully!');
  } catch (error) {
    console.error('🔧 DEBUG: Scraping failed with error:', error);
    if (error instanceof Error) {
      console.error('🔧 DEBUG: Error message:', error.message);
      console.error('🔧 DEBUG: Error stack:', error.stack);
    }
  }
}

debugScraper();
