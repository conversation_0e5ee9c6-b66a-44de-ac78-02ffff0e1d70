import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function deleteScrapingLogs() {
  try {
    console.log('🗑️  Starting to delete scraping logs...');
    
    // First, count how many logs exist
    const totalLogs = await prisma.scrapingLog.count();
    console.log(`📊 Found ${totalLogs} scraping log(s) in the database`);
    
    if (totalLogs === 0) {
      console.log('✅ No scraping logs to delete');
      return;
    }
    
    // Show some recent logs before deletion (for confirmation)
    console.log('\n📋 Recent scraping logs to be deleted:');
    const recentLogs = await prisma.scrapingLog.findMany({
      orderBy: { startTime: 'desc' },
      take: 5,
      select: {
        id: true,
        startTime: true,
        status: true,
        domainsFound: true,
        errorMessage: true
      }
    });
    
    recentLogs.forEach((log, i) => {
      console.log(`   ${i + 1}. ${log.startTime.toLocaleString()} - ${log.status} (${log.domainsFound || 0} domains)`);
      if (log.errorMessage) {
        console.log(`      Error: ${log.errorMessage.substring(0, 100)}...`);
      }
    });
    
    // Delete all scraping logs
    console.log('\n🗑️  Deleting all scraping logs...');
    const deleteResult = await prisma.scrapingLog.deleteMany({});
    
    console.log(`✅ Successfully deleted ${deleteResult.count} scraping log(s)`);
    
    // Verify deletion
    const remainingLogs = await prisma.scrapingLog.count();
    console.log(`📊 Remaining scraping logs: ${remainingLogs}`);
    
    if (remainingLogs === 0) {
      console.log('🎉 All scraping logs have been successfully deleted!');
    } else {
      console.log('⚠️  Warning: Some scraping logs may still remain');
    }
    
  } catch (error) {
    console.error('❌ Error deleting scraping logs:', error);
    
    if (error.code === 'P2025') {
      console.log('💡 No scraping logs found to delete');
    } else {
      console.log('💡 Please check your database connection and try again');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
deleteScrapingLogs();
