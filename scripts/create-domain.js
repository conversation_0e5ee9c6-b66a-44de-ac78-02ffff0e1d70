import { PrismaClient } from '@prisma/client';
import { parse } from 'date-fns';

const prisma = new PrismaClient();

async function createDomainWithCustomDate() {
  try {
    // Parse the specific date you want to use: "02.06.2025 06:52"
    const customCreatedAt = parse('02.06.2025 06:52', 'dd.MM.yyyy HH:mm', new Date());
    
    // Parse the expiration date from the data-sort-value (Unix timestamp)
    const expirationTimestamp = 1748839941; // From your HTML example
    const expirationDate = new Date(expirationTimestamp * 1000);
    
    console.log('🔧 Creating domain with custom createdAt date...');
    console.log(`📅 Custom createdAt: ${customCreatedAt.toISOString()}`);
    console.log(`📅 Expiration date: ${expirationDate.toISOString()}`);
    
    // Calculate domain rating (same logic as scraper)
    const domainName = 'murerjobs.dk';
    const domainAge = '6 år';
    
    // Rating calculation
    let rating = 0;
    const length = domainName.replace('.dk', '').length;
    
    // Length scoring (shorter = better)
    if (length <= 3) rating += 100;
    else if (length <= 5) rating += 80;
    else if (length <= 7) rating += 60;
    else if (length <= 10) rating += 40;
    else rating += 20;
    
    // Bonus points
    if (!/\d/.test(domainName)) rating += 20; // No numbers
    if (!/-/.test(domainName)) rating += 10;  // No hyphens
    
    const isHighlighted = length <= 3;
    
    console.log(`🎯 Domain rating: ${rating}`);
    console.log(`⭐ Highlighted: ${isHighlighted}`);
    
    // Create the domain with custom createdAt
    const domain = await prisma.domain.create({
      data: {
        domainName,
        expirationDate,
        domainAge,
        collectionDate: customCreatedAt, // Use custom date for collection
        domainRating: rating,
        isHighlighted,
        createdAt: customCreatedAt, // Use your specific date
        updatedAt: customCreatedAt  // Also set updatedAt to the same date
      }
    });
    
    console.log('✅ Domain created successfully!');
    console.log(`📝 Domain ID: ${domain.id}`);
    console.log(`🌐 Domain: ${domain.domainName}`);
    console.log(`📅 Created at: ${domain.createdAt.toISOString()}`);
    console.log(`📅 Collection date: ${domain.collectionDate.toISOString()}`);
    
  } catch (error) {
    console.error('❌ Error creating domain:', error);
    
    if (error.code === 'P2002') {
      console.log('💡 This domain already exists for this collection date.');
      console.log('   Try updating the existing record instead.');
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the function
createDomainWithCustomDate();
