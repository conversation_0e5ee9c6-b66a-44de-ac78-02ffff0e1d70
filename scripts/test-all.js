import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

console.log('🧪 Running comprehensive tests for Domains Expired Tracker...\n');

const testResults = {
  passed: 0,
  failed: 0,
  errors: []
};

// Function to run a command and capture output
function runCommand(command, description) {
  console.log(`📋 ${description}...`);
  try {
    const output = execSync(command, { 
      stdio: 'pipe', 
      encoding: 'utf8',
      timeout: 60000 
    });
    console.log(`✅ ${description} - PASSED\n`);
    testResults.passed++;
    return { success: true, output };
  } catch (error) {
    console.log(`❌ ${description} - FAILED`);
    console.log(`Error: ${error.message}\n`);
    testResults.failed++;
    testResults.errors.push({ test: description, error: error.message });
    return { success: false, error: error.message };
  }
}

// Function to check file existence
function checkFile(filePath, description) {
  console.log(`📁 Checking ${description}...`);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${description} - EXISTS\n`);
    testResults.passed++;
    return true;
  } else {
    console.log(`❌ ${description} - MISSING\n`);
    testResults.failed++;
    testResults.errors.push({ test: description, error: 'File not found' });
    return false;
  }
}

// Function to check directory structure
function checkDirectoryStructure() {
  console.log('🏗️  Checking project structure...\n');
  
  const requiredFiles = [
    { path: 'package.json', desc: 'Package configuration' },
    { path: 'tsconfig.json', desc: 'TypeScript configuration' },
    { path: 'jest.config.js', desc: 'Jest configuration' },
    { path: 'prisma/schema.prisma', desc: 'Prisma schema' },
    { path: '.env', desc: 'Environment variables' },
    { path: 'app/root.tsx', desc: 'React Router root' },
    { path: 'app/routes.ts', desc: 'Routes configuration' },
    { path: 'app/lib/db.server.ts', desc: 'Database connection' },
    { path: 'app/services/scraper.ts', desc: 'Domain scraper service' },
    { path: 'app/services/cron.ts', desc: 'Cron service' },
    { path: 'app/routes/home.tsx', desc: 'Home page route' },
    { path: 'app/routes/domains.tsx', desc: 'Domains page route' },
    { path: 'app/routes/api.domains.tsx', desc: 'Domains API route' },
    { path: 'app/routes/api.scrape.tsx', desc: 'Scrape API route' },
  ];

  const requiredDirs = [
    { path: 'app', desc: 'Application directory' },
    { path: 'app/lib', desc: 'Library directory' },
    { path: 'app/routes', desc: 'Routes directory' },
    { path: 'app/services', desc: 'Services directory' },
    { path: 'tests', desc: 'Tests directory' },
    { path: 'scripts', desc: 'Scripts directory' },
    { path: 'prisma', desc: 'Prisma directory' },
  ];

  requiredDirs.forEach(({ path, desc }) => {
    checkFile(path, `${desc} directory`);
  });

  requiredFiles.forEach(({ path, desc }) => {
    checkFile(path, desc);
  });
}

// Function to validate package.json
function validatePackageJson() {
  console.log('📦 Validating package.json...\n');
  
  try {
    const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    const requiredDeps = [
      '@prisma/client',
      'react-router',
      'cheerio',
      'axios',
      'node-cron',
      'date-fns'
    ];

    const requiredDevDeps = [
      'typescript',
      'prisma',
      'tsx',
      '@types/node',
      'jest',
      'ts-jest'
    ];

    const requiredScripts = [
      'dev',
      'build',
      'start',
      'test',
      'db:generate',
      'db:push',
      'scraper:run',
      'cron:start'
    ];

    let allValid = true;

    requiredDeps.forEach(dep => {
      if (!packageJson.dependencies || !packageJson.dependencies[dep]) {
        console.log(`❌ Missing dependency: ${dep}`);
        allValid = false;
      }
    });

    requiredDevDeps.forEach(dep => {
      if (!packageJson.devDependencies || !packageJson.devDependencies[dep]) {
        console.log(`❌ Missing dev dependency: ${dep}`);
        allValid = false;
      }
    });

    requiredScripts.forEach(script => {
      if (!packageJson.scripts || !packageJson.scripts[script]) {
        console.log(`❌ Missing script: ${script}`);
        allValid = false;
      }
    });

    if (allValid) {
      console.log('✅ Package.json validation - PASSED\n');
      testResults.passed++;
    } else {
      console.log('❌ Package.json validation - FAILED\n');
      testResults.failed++;
      testResults.errors.push({ test: 'Package.json validation', error: 'Missing required dependencies or scripts' });
    }

  } catch (error) {
    console.log(`❌ Package.json validation - FAILED: ${error.message}\n`);
    testResults.failed++;
    testResults.errors.push({ test: 'Package.json validation', error: error.message });
  }
}

// Function to run TypeScript compilation check
function checkTypeScript() {
  return runCommand('npm run typecheck', 'TypeScript compilation check');
}

// Function to run Jest tests
function runJestTests() {
  return runCommand('npm test', 'Jest unit tests');
}

// Function to check environment configuration
function checkEnvironment() {
  console.log('🌍 Checking environment configuration...\n');
  
  try {
    const envContent = fs.readFileSync('.env', 'utf8');
    const requiredVars = [
      'DATABASE_URL',
      'NODE_ENV',
      'SCRAPING_ENABLED',
      'SIMPLY_URL'
    ];

    let allPresent = true;
    requiredVars.forEach(varName => {
      if (!envContent.includes(varName)) {
        console.log(`❌ Missing environment variable: ${varName}`);
        allPresent = false;
      }
    });

    if (allPresent) {
      console.log('✅ Environment configuration - PASSED\n');
      testResults.passed++;
    } else {
      console.log('❌ Environment configuration - FAILED\n');
      testResults.failed++;
      testResults.errors.push({ test: 'Environment configuration', error: 'Missing required environment variables' });
    }

  } catch (error) {
    console.log(`❌ Environment configuration - FAILED: ${error.message}\n`);
    testResults.failed++;
    testResults.errors.push({ test: 'Environment configuration', error: error.message });
  }
}

// Function to test scraper functionality (without database)
function testScraperLogic() {
  return runCommand('npm run scraper:test', 'Scraper functionality test');
}

// Main test execution
async function runAllTests() {
  console.log('🚀 Starting comprehensive test suite...\n');
  
  // 1. Check project structure
  checkDirectoryStructure();
  
  // 2. Validate package.json
  validatePackageJson();
  
  // 3. Check environment
  checkEnvironment();
  
  // 4. Install dependencies if needed
  if (!fs.existsSync('node_modules')) {
    runCommand('npm install', 'Installing dependencies');
  }
  
  // 5. Generate Prisma client
  runCommand('npm run db:generate', 'Generating Prisma client');
  
  // 6. Run TypeScript check
  checkTypeScript();
  
  // 7. Run Jest tests
  runJestTests();
  
  // 8. Test scraper logic
  testScraperLogic();
  
  // Print final results
  console.log('\n' + '='.repeat(60));
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('='.repeat(60));
  console.log(`✅ Passed: ${testResults.passed}`);
  console.log(`❌ Failed: ${testResults.failed}`);
  console.log(`📈 Success Rate: ${Math.round((testResults.passed / (testResults.passed + testResults.failed)) * 100)}%`);
  
  if (testResults.errors.length > 0) {
    console.log('\n🔍 FAILED TESTS:');
    testResults.errors.forEach((error, index) => {
      console.log(`${index + 1}. ${error.test}: ${error.error}`);
    });
  }
  
  console.log('\n' + '='.repeat(60));
  
  if (testResults.failed === 0) {
    console.log('🎉 All tests passed! The application is ready to use.');
    process.exit(0);
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.');
    process.exit(1);
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('💥 Test runner failed:', error);
  process.exit(1);
});
