import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkDatabase() {
  console.log('🔍 Checking database contents...\n');

  try {
    // Check database connection
    console.log('📡 Testing database connection...');
    await prisma.$connect();
    console.log('✅ Database connection successful\n');

    // Check domains table
    console.log('📊 Checking domains table...');
    const totalDomains = await prisma.domain.count();
    console.log(`   Total domains: ${totalDomains}`);

    if (totalDomains > 0) {
      // Get recent domains
      const recentDomains = await prisma.domain.findMany({
        orderBy: { createdAt: 'desc' },
        take: 10
      });

      console.log('\n📋 Recent domains:');
      recentDomains.forEach((domain, i) => {
        console.log(`   ${i + 1}. ${domain.domainName} (collected: ${domain.collectionDate.toLocaleDateString()}, rating: ${domain.domainRating})`);
      });

      // Check highlighted domains
      const highlightedCount = await prisma.domain.count({
        where: { isHighlighted: true }
      });
      console.log(`\n⭐ Highlighted domains: ${highlightedCount}`);

      // Check domains by collection date
      const domainsByDate = await prisma.domain.groupBy({
        by: ['collectionDate'],
        _count: { id: true },
        orderBy: { collectionDate: 'desc' },
        take: 5
      });

      console.log('\n📅 Domains by collection date:');
      domainsByDate.forEach(group => {
        console.log(`   ${group.collectionDate.toLocaleDateString()}: ${group._count.id} domains`);
      });
    }

    // Check scraping logs
    console.log('\n📝 Checking scraping logs...');
    const totalLogs = await prisma.scrapingLog.count();
    console.log(`   Total scraping attempts: ${totalLogs}`);

    if (totalLogs > 0) {
      const recentLogs = await prisma.scrapingLog.findMany({
        orderBy: { startTime: 'desc' },
        take: 5
      });

      console.log('\n📋 Recent scraping logs:');
      recentLogs.forEach((log, i) => {
        const duration = log.endTime 
          ? `${Math.round((log.endTime.getTime() - log.startTime.getTime()) / 1000)}s`
          : 'running';
        console.log(`   ${i + 1}. ${log.startTime.toLocaleString()} - ${log.status} (${log.domainsFound || 0} domains, ${duration})`);
        if (log.errorMessage) {
          console.log(`      Error: ${log.errorMessage}`);
        }
      });
    }

    // Database statistics
    console.log('\n📊 Database Statistics:');
    console.log(`   Total domains: ${totalDomains}`);
    console.log(`   Total scraping logs: ${totalLogs}`);
    
    if (totalDomains > 0) {
      const oldestDomain = await prisma.domain.findFirst({
        orderBy: { collectionDate: 'asc' }
      });
      const newestDomain = await prisma.domain.findFirst({
        orderBy: { collectionDate: 'desc' }
      });
      
      console.log(`   Data range: ${oldestDomain?.collectionDate.toLocaleDateString()} to ${newestDomain?.collectionDate.toLocaleDateString()}`);
    }

  } catch (error) {
    console.error('❌ Database check failed:', error);
    if (error instanceof Error) {
      console.error('❌ Error details:', error.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

// Run the check
checkDatabase()
  .then(() => {
    console.log('\n✅ Database check completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n❌ Database check failed:', error);
    process.exit(1);
  });
