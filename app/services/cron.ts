import cron from 'node-cron';
import { DomainScraper } from './scraper.js';

export class CronService {
  private scraper: DomainScraper;
  private isRunning = false;

  constructor() {
    this.scraper = new DomainScraper();
  }

  start(): void {
    if (this.isRunning) {
      console.log('Cron service is already running');
      return;
    }

    const scrapingEnabled = process.env.SCRAPING_ENABLED === 'true';
    const intervalHours = parseInt(process.env.SCRAPING_INTERVAL_HOURS || '1');

    if (!scrapingEnabled) {
      console.log('Scraping is disabled via environment variable');
      return;
    }

    console.log(`Starting cron service with ${intervalHours} hour interval`);

    // Run every hour at minute 0
    const cronExpression = `0 */${intervalHours} * * *`;
    
    cron.schedule(cronExpression, async () => {
      console.log('Running scheduled domain scraping...');
      try {
        await this.scraper.runScraping();
        console.log('Scheduled scraping completed successfully');
      } catch (error) {
        console.error('Scheduled scraping failed:', error);
      }
    }, {
      scheduled: true,
      timezone: "Europe/Copenhagen"
    });

    this.isRunning = true;
    console.log('Cron service started successfully');

    // Run initial scraping
    this.runInitialScraping();
  }

  private async runInitialScraping(): Promise<void> {
    console.log('Running initial domain scraping...');
    try {
      await this.scraper.runScraping();
      console.log('Initial scraping completed successfully');
    } catch (error) {
      console.error('Initial scraping failed:', error);
    }
  }

  stop(): void {
    if (!this.isRunning) {
      console.log('Cron service is not running');
      return;
    }

    // Note: node-cron doesn't have a destroy method, tasks are automatically cleaned up
    this.isRunning = false;
    console.log('Cron service stopped');
  }
}

// CLI execution
if (import.meta.url === `file://${process.argv[1]}`) {
  const cronService = new CronService();
  
  // Handle graceful shutdown
  process.on('SIGINT', () => {
    console.log('Received SIGINT, shutting down gracefully...');
    cronService.stop();
    process.exit(0);
  });

  process.on('SIGTERM', () => {
    console.log('Received SIGTERM, shutting down gracefully...');
    cronService.stop();
    process.exit(0);
  });

  cronService.start();
  
  // Keep the process alive
  console.log('Cron service is running. Press Ctrl+C to stop.');
}
