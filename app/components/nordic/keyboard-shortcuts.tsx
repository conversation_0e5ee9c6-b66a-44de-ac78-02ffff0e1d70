import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card"
import { Badge } from "~/components/ui/badge"
import { Keyboard } from "lucide-react"

interface ShortcutProps {
  keys: string[]
  description: string
}

function Shortcut({ keys, description }: ShortcutProps) {
  return (
    <div className="flex items-center justify-between py-2">
      <span className="text-sm text-muted-foreground">{description}</span>
      <div className="flex gap-1">
        {keys.map((key, index) => (
          <React.Fragment key={key}>
            {index > 0 && <span className="text-muted-foreground">+</span>}
            <Badge variant="outline" className="text-xs font-mono">
              {key}
            </Badge>
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

export function KeyboardShortcuts() {
  return (
    <Card className="w-80">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Keyboard className="h-4 w-4" />
          Keyboard Shortcuts
        </CardTitle>
        <CardDescription>
          Speed up your workflow with these shortcuts
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-1">
        <Shortcut keys={["Ctrl", "K"]} description="Focus search" />
        <Shortcut keys={["Ctrl", "R"]} description="Refresh page" />
        <Shortcut keys={["Esc"]} description="Clear search" />
        <Shortcut keys={["Tab"]} description="Navigate filters" />
      </CardContent>
    </Card>
  )
}
