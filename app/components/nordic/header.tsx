import { <PERSON> } from "react-router"
import { But<PERSON> } from "~/components/ui/button"
import { ThemeToggle } from "~/components/nordic/theme-toggle"
import { cn } from "~/lib/utils"

interface HeaderProps {
  title: string
  subtitle?: string
  backLink?: string
  actions?: React.ReactNode
  className?: string
}

export function Header({ 
  title, 
  subtitle, 
  backLink, 
  actions,
  className 
}: HeaderProps) {
  return (
    <div className={cn("flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8", className)}>
      <div className="space-y-1">
        <div className="flex items-center gap-4">
          {backLink && (
            <Button variant="nordic-ghost" size="sm" asChild>
              <Link to={backLink}>
                ← Back
              </Link>
            </Button>
          )}
          <h1 className="text-3xl font-bold tracking-tight text-foreground nordic-text-gradient">
            {title}
          </h1>
        </div>
        {subtitle && (
          <p className="text-muted-foreground">
            {subtitle}
          </p>
        )}
      </div>
      <div className="flex items-center gap-2">
        {actions}
        <ThemeToggle />
      </div>
    </div>
  )
}
