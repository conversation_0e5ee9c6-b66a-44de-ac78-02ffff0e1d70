import * as React from "react"
import { Search, X } from "lucide-react"
import { Input } from "~/components/ui/input"
import { But<PERSON> } from "~/components/ui/button"
import { cn } from "~/lib/utils"

interface EnhancedSearchProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  debounceMs?: number
}

export function EnhancedSearch({ 
  value, 
  onChange, 
  placeholder = "Search...", 
  className,
  debounceMs = 300 
}: EnhancedSearchProps) {
  const [localValue, setLocalValue] = React.useState(value)
  const [isSearching, setIsSearching] = React.useState(false)
  const timeoutRef = React.useRef<NodeJS.Timeout>()

  React.useEffect(() => {
    setLocalValue(value)
  }, [value])

  React.useEffect(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    if (localValue !== value) {
      setIsSearching(true)
      timeoutRef.current = setTimeout(() => {
        onChange(localValue)
        setIsSearching(false)
      }, debounceMs)
    }

    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [localValue, onChange, debounceMs, value])

  const handleClear = () => {
    setLocalValue("")
    onChange("")
  }

  return (
    <div className={cn("relative", className)}>
      <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
      <Input
        type="text"
        value={localValue}
        onChange={(e) => setLocalValue(e.target.value)}
        placeholder={placeholder}
        className="pl-10 pr-10"
        aria-label="Search domains"
      />
      {localValue && (
        <Button
          variant="ghost"
          size="sm"
          className="absolute right-1 top-1/2 h-8 w-8 -translate-y-1/2 p-0"
          onClick={handleClear}
          aria-label="Clear search"
        >
          <X className="h-4 w-4" />
        </Button>
      )}
      {isSearching && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="h-4 w-4 animate-spin rounded-full border-2 border-primary border-t-transparent" />
        </div>
      )}
    </div>
  )
}
