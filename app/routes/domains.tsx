import { type MetaFunction, type LoaderFunctionArgs } from "react-router";
import { useLoaderData, Link, useSearchParams, Form } from "react-router";
import { prisma } from "../lib/db.server";
import { format, startOfDay, endOfDay, parseISO } from "date-fns";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { Header } from "~/components/nordic/header";
import { Search, Filter, Star, Calendar, RefreshCw, Database } from "lucide-react";
import { EnhancedSearch } from "~/components/nordic/enhanced-search";
import { EmptyState } from "~/components/nordic/empty-state";

export const meta: MetaFunction = () => {
  return [
    { title: "Domain List - Domains Expired Tracker" },
    { name: "description", content: "Browse expired domains by date with search and filtering" },
  ];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const dateParam = params.date || format(new Date(), 'yyyy-MM-dd');
  const search = url.searchParams.get("search") || "";
  const highlighted = url.searchParams.get("highlighted") === "true";
  const domainAge = url.searchParams.get("domainAge") || "";
  const minRating = url.searchParams.get("minRating") || "";
  const maxRating = url.searchParams.get("maxRating") || "";
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = 50;

  try {
    const targetDate = parseISO(dateParam);
    let whereClause: any = {
      createdAt: {
        gte: startOfDay(targetDate),
        lte: endOfDay(targetDate),
      },
    };

    if (search) {
      whereClause.domainName = {
        contains: search,
        mode: "insensitive",
      };
    }

    if (highlighted) {
      whereClause.isHighlighted = true;
    }

    if (domainAge) {
      whereClause.domainAge = {
        contains: domainAge,
        mode: "insensitive",
      };
    }

    if (minRating) {
      whereClause.domainRating = {
        ...whereClause.domainRating,
        gte: parseInt(minRating),
      };
    }

    if (maxRating) {
      whereClause.domainRating = {
        ...whereClause.domainRating,
        lte: parseInt(maxRating),
      };
    }

    const [domains, totalCount, availableDates] = await Promise.all([
      prisma.domain.findMany({
        where: whereClause,
        orderBy: [
          { isHighlighted: "desc" },
          { domainRating: "desc" },
          { domainName: "asc" },
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.domain.count({ where: whereClause }),
      prisma.domain.findMany({
        select: {
          createdAt: true,
        },
        distinct: ['createdAt'],
        orderBy: {
          createdAt: 'desc',
        },
        take: 30,
      }),
    ]);

    return Response.json({
      domains,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
      currentDate: dateParam,
      search,
      highlighted,
      domainAge,
      minRating,
      maxRating,
      availableDates: availableDates.map(d => format(d.createdAt, 'yyyy-MM-dd')),
    });
  } catch (error) {
    console.error("Error loading domains:", error);
    return Response.json({
      domains: [],
      pagination: { page: 1, limit, total: 0, totalPages: 0 },
      currentDate: dateParam,
      search,
      highlighted,
      domainAge,
      minRating,
      maxRating,
      availableDates: [],
    });
  }
}

export default function Domains() {
  const data = useLoaderData<typeof loader>();
  const { domains, pagination, currentDate, search, highlighted, domainAge, minRating, maxRating, availableDates } = data || {
    domains: [],
    pagination: { page: 1, limit: 50, total: 0, totalPages: 0 },
    currentDate: new Date().toISOString().split('T')[0],
    search: '',
    highlighted: false,
    domainAge: '',
    minRating: '',
    maxRating: '',
    availableDates: []
  };
  const [searchParams, setSearchParams] = useSearchParams();

  const updateSearchParams = (updates: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams);
    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === "") {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });
    setSearchParams(newParams);
  };

  return (
    <div className="min-h-screen nordic-gradient">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <Header
          title={`Domains for ${format(new Date(currentDate), 'MMMM dd, yyyy')}`}
          subtitle={`${pagination.total} domains found${highlighted ? " (highlighted only)" : ""}${search ? ` (filtered by "${search}")` : ""}${domainAge ? ` (age: ${domainAge})` : ""}${minRating || maxRating ? ` (rating: ${minRating || '0'}-${maxRating || '∞'})` : ""}`}
          backLink="/"
          actions={
            <Button variant="nordic-outline" asChild>
              <Link to="/">
                ← Back to Home
              </Link>
            </Button>
          }
        />

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters
            </CardTitle>
            <CardDescription>
              Refine your domain search
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-4">
              {/* Date Selector */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Date
                </label>
                <Select
                  value={currentDate}
                  onValueChange={(value) => window.location.href = `/domains/${value}${window.location.search}`}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableDates.map((date) => (
                      <SelectItem key={date} value={date}>
                        {format(new Date(date), 'MMM dd, yyyy')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Search Domains
                </label>
                <Input
                  type="text"
                  value={search}
                  onChange={(e) => updateSearchParams({ search: e.target.value, page: "1" })}
                  placeholder="Enter domain name..."
                />
              </div>

              {/* Highlighted Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Filter
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="highlighted"
                    checked={highlighted}
                    onChange={(e) => updateSearchParams({
                      highlighted: e.target.checked ? "true" : null,
                      page: "1"
                    })}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="highlighted" className="text-sm">
                    Highlighted only (≤3 chars)
                  </label>
                </div>
              </div>

              {/* Domain Age Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Domain Age
                </label>
                <Input
                  type="text"
                  value={domainAge}
                  onChange={(e) => updateSearchParams({ domainAge: e.target.value, page: "1" })}
                  placeholder="e.g., 6 år"
                />
              </div>

              {/* Min Rating Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Min Rating
                </label>
                <Input
                  type="number"
                  value={minRating}
                  onChange={(e) => updateSearchParams({ minRating: e.target.value, page: "1" })}
                  placeholder="0"
                  min="0"
                  max="200"
                />
              </div>

              {/* Max Rating Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Max Rating
                </label>
                <Input
                  type="number"
                  value={maxRating}
                  onChange={(e) => updateSearchParams({ maxRating: e.target.value, page: "1" })}
                  placeholder="200"
                  min="0"
                  max="200"
                />
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => updateSearchParams({
                    search: null,
                    highlighted: null,
                    domainAge: null,
                    minRating: null,
                    maxRating: null,
                    page: "1"
                  })}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Domains List */}
        <Card className="overflow-hidden">
          <CardHeader>
            <CardTitle>Domain Results</CardTitle>
            <CardDescription>
              {pagination.total} domains found
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Domæne</TableHead>
                  <TableHead>Tilføjet</TableHead>
                  <TableHead>Domænealder</TableHead>
                  <TableHead>Rating</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {domains.map((domain) => (
                  <TableRow
                    key={domain.id}
                    className={domain.isHighlighted ? "bg-nordic-aurora-green/5" : ""}
                  >
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <span className="font-medium">
                          {domain.domainName}
                        </span>
                        {domain.isHighlighted && (
                          <Badge variant="nordic" className="text-xs">
                            <Star className="h-3 w-3 mr-1" />
                            Highlighted
                          </Badge>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {format(new Date(domain.createdAt), 'dd.MM.yyyy HH:mm')}
                    </TableCell>
                    <TableCell>
                      {domain.domainAge}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">
                        {domain.domainRating}
                      </Badge>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>

          {/* Pagination */}
          {pagination.totalPages > 1 && (
            <div className="px-6 py-4 border-t flex items-center justify-between">
              <div className="flex-1 flex justify-between sm:hidden">
                {pagination.page > 1 && (
                  <Button
                    variant="outline"
                    onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                  >
                    Previous
                  </Button>
                )}
                {pagination.page < pagination.totalPages && (
                  <Button
                    variant="outline"
                    onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                  >
                    Next
                  </Button>
                )}
              </div>
              <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">
                    Showing{' '}
                    <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                    {' '}to{' '}
                    <span className="font-medium">
                      {Math.min(pagination.page * pagination.limit, pagination.total)}
                    </span>
                    {' '}of{' '}
                    <span className="font-medium">{pagination.total}</span>
                    {' '}results
                  </p>
                </div>
                <div className="flex gap-2">
                  {pagination.page > 1 && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                    >
                      Previous
                    </Button>
                  )}
                  {pagination.page < pagination.totalPages && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                    >
                      Next
                    </Button>
                  )}
                </div>
              </div>
            </div>
          )}
        </Card>

        {domains.length === 0 && (
          <Card className="mt-6">
            <CardContent className="text-center py-12">
              <div className="flex flex-col items-center gap-4">
                <Search className="h-12 w-12 text-muted-foreground" />
                <div>
                  <h3 className="text-lg font-semibold">No domains found</h3>
                  <p className="text-muted-foreground">
                    No domains match the selected criteria. Try adjusting your filters.
                  </p>
                </div>
                <Button
                  variant="outline"
                  onClick={() => updateSearchParams({
                    search: null,
                    highlighted: null,
                    domainAge: null,
                    minRating: null,
                    maxRating: null,
                    page: "1"
                  })}
                >
                  Clear Filters
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
