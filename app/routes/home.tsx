import { type MetaFunction, type LoaderFunctionArgs } from "react-router";
import { useLoaderData, Link, Form } from "react-router";
import { prisma } from "../lib/db.server";
import { format, startOfDay, endOfDay } from "date-fns";

export const meta: MetaFunction = () => {
  return [
    { title: "Domains Expired - Track Expired Domains from Simply.com" },
    { name: "description", content: "Track and monitor expired domains from Simply.com with automated data collection" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const today = new Date();
  const todayStart = startOfDay(today);
  const todayEnd = endOfDay(today);

  try {
    // Get today's domains
    const [todayDomains, highlightedDomains, totalDomains, availableDates] = await Promise.all([
      prisma.domain.count({
        where: {
          collectionDate: {
            gte: todayStart,
            lte: todayEnd,
          },
        },
      }),
      prisma.domain.count({
        where: {
          collectionDate: {
            gte: todayStart,
            lte: todayEnd,
          },
          isHighlighted: true,
        },
      }),
      prisma.domain.count(),
      prisma.domain.findMany({
        select: {
          collectionDate: true,
        },
        distinct: ['collectionDate'],
        orderBy: {
          collectionDate: 'desc',
        },
        take: 10,
      }),
    ]);

    // Get recent scraping logs
    const recentLogs = await prisma.scrapingLog.findMany({
      orderBy: {
        startTime: 'desc',
      },
      take: 5,
    });

    return Response.json({
      stats: {
        todayDomains,
        highlightedDomains,
        totalDomains,
      },
      availableDates: availableDates.map(d => format(d.collectionDate, 'yyyy-MM-dd')),
      recentLogs,
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  } catch (error) {
    console.error("Error loading home data:", error);
    return Response.json({
      stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
      availableDates: [],
      recentLogs: [],
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  }
}

export default function Home() {
  const data = useLoaderData<typeof loader>();
  const { stats, availableDates, recentLogs, todayDate } = data || {
    stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
    availableDates: [],
    recentLogs: [],
    todayDate: new Date().toISOString().split('T')[0]
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Domains Expired Tracker
          </h1>
          <p className="text-lg text-gray-600">
            Track expired domains from Simply.com with automated hourly updates
          </p>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Today's Domains</h3>
            <p className="text-3xl font-bold text-blue-600">{stats.todayDomains}</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Highlighted Domains</h3>
            <p className="text-3xl font-bold text-green-600">{stats.highlightedDomains}</p>
            <p className="text-sm text-gray-500">3 characters or less</p>
          </div>
          <div className="bg-white rounded-lg shadow p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Total Domains</h3>
            <p className="text-3xl font-bold text-purple-600">{stats.totalDomains}</p>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h2>
          <div className="flex flex-wrap gap-4">
            <Link
              to={`/domains/${todayDate}`}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
            >
              View Today's Domains
            </Link>
            <Link
              to={`/domains/${todayDate}?highlighted=true`}
              className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
            >
              View Highlighted Domains
            </Link>
            <Form method="post" action="/api/scrape" className="inline">
              <button
                type="submit"
                className="bg-orange-600 text-white px-4 py-2 rounded-lg hover:bg-orange-700 transition-colors"
              >
                Manual Scrape
              </button>
            </Form>
          </div>
        </div>

        {/* Available Dates */}
        <div className="bg-white rounded-lg shadow p-6 mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Available Dates</h2>
          <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
            {availableDates.map((date) => (
              <Link
                key={date}
                to={`/domains/${date}`}
                className="text-center p-3 border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-300 transition-colors"
              >
                {format(new Date(date), 'MMM dd, yyyy')}
              </Link>
            ))}
          </div>
        </div>

        {/* Recent Scraping Logs */}
        <div className="bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">Recent Scraping Activity</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Start Time
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Domains Found
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {recentLogs.map((log) => (
                  <tr key={log.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {format(new Date(log.startTime), 'MMM dd, yyyy HH:mm')}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                          log.status === 'completed'
                            ? 'bg-green-100 text-green-800'
                            : log.status === 'failed'
                            ? 'bg-red-100 text-red-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}
                      >
                        {log.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.domainsFound || '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.endTime
                        ? `${Math.round(
                            (new Date(log.endTime).getTime() - new Date(log.startTime).getTime()) / 1000
                          )}s`
                        : '-'}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}
