import * as React from "react";
import { type MetaFunction, type LoaderFunctionArgs, type ActionFunctionArgs } from "react-router";
import { useLoaderData, Form, useSearchParams, redirect } from "react-router";
import { prisma } from "../lib/db.server";
import { format, startOfDay, endOfDay, parseISO } from "date-fns";
import { DomainScraper } from "../services/scraper";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { StatsCard } from "~/components/nordic/stats-card";
import { Header } from "~/components/nordic/header";
import { Database, Globe, Star, Activity, RefreshCw, Search, Filter, Calendar } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { StatsCardSkeleton, TableSkeleton } from "~/components/nordic/loading-states";
import { EmptyState } from "~/components/nordic/empty-state";
import { EnhancedSearch } from "~/components/nordic/enhanced-search";
import { Input } from "~/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";

export const meta: MetaFunction = () => {
  return [
    { title: "Domains Expired - Track Expired Domains" },
    { name: "description", content: "Track and monitor expired domains with automated data collection" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const today = new Date();
  const todayStart = startOfDay(today);
  const todayEnd = endOfDay(today);

  // Get filter parameters
  const dateParam = url.searchParams.get("date") || format(today, 'yyyy-MM-dd');
  const search = url.searchParams.get("search") || "";
  const highlighted = url.searchParams.get("highlighted") === "true";
  const domainAge = url.searchParams.get("domainAge") || "";
  const minRating = url.searchParams.get("minRating") || "";
  const maxRating = url.searchParams.get("maxRating") || "";
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = 50;

  try {
    // Build where clause for domains
    const targetDate = parseISO(dateParam);
    let whereClause: any = {
      createdAt: {
        gte: startOfDay(targetDate),
        lte: endOfDay(targetDate),
      },
    };

    if (search) {
      whereClause.domainName = {
        contains: search,
        mode: "insensitive",
      };
    }

    if (highlighted) {
      whereClause.isHighlighted = true;
    }

    if (domainAge) {
      whereClause.domainAge = {
        contains: domainAge,
        mode: "insensitive",
      };
    }

    if (minRating) {
      whereClause.domainRating = {
        ...whereClause.domainRating,
        gte: parseInt(minRating),
      };
    }

    if (maxRating) {
      whereClause.domainRating = {
        ...whereClause.domainRating,
        lte: parseInt(maxRating),
      };
    }

    // Get all data in parallel
    const [
      domains,
      totalCount,
      todayDomains,
      highlightedDomains,
      totalDomains,
      availableDates
    ] = await Promise.all([
      // Filtered domains with pagination
      prisma.domain.findMany({
        where: whereClause,
        orderBy: [
          { isHighlighted: 'desc' },
          { domainRating: 'desc' },
          { createdAt: 'desc' }
        ],
        skip: (page - 1) * limit,
        take: limit,
      }),
      // Total count for pagination
      prisma.domain.count({ where: whereClause }),
      // Today's stats
      prisma.domain.count({
        where: {
          createdAt: {
            gte: todayStart,
            lte: todayEnd,
          },
        },
      }),
      prisma.domain.count({
        where: {
          createdAt: {
            gte: todayStart,
            lte: todayEnd,
          },
          isHighlighted: true,
        },
      }),
      prisma.domain.count(),
      // Available dates - get unique dates
      prisma.$queryRaw`
        SELECT DISTINCT DATE(created_at) as date
        FROM domains
        ORDER BY DATE(created_at) DESC
        LIMIT 30
      `,
    ]);

    const totalPages = Math.ceil(totalCount / limit);

    return Response.json({
      domains,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
      },
      currentDate: dateParam,
      search,
      highlighted,
      domainAge,
      minRating,
      maxRating,
      stats: {
        todayDomains,
        highlightedDomains,
        totalDomains,
      },
      availableDates: availableDates.map((d: any) => format(new Date(d.date), 'yyyy-MM-dd')),
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  } catch (error) {
    console.error("Error loading data:", error);
    return Response.json({
      domains: [],
      pagination: { page: 1, limit, total: 0, totalPages: 0 },
      currentDate: dateParam,
      search,
      highlighted,
      domainAge,
      minRating,
      maxRating,
      stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
      availableDates: [format(today, 'yyyy-MM-dd')],
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  }
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return Response.json({ error: "Method not allowed" }, { status: 405 });
  }

  const formData = await request.formData();
  const actionType = formData.get("action");

  if (actionType === "scrape") {
    try {
      console.log("🚀 Manual scraping triggered");
      const scraper = new DomainScraper();
      await scraper.runScraping();

      return redirect("/?success=scrape");
    } catch (error) {
      console.error("Manual scraping failed:", error);
      return redirect("/?error=scrape");
    }
  }

  return Response.json({ error: "Invalid action" }, { status: 400 });
}

export default function Home() {
  const data = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();

  const {
    domains,
    pagination,
    currentDate,
    search,
    highlighted,
    domainAge,
    minRating,
    maxRating,
    stats,
    availableDates,
    todayDate
  } = data || {
    domains: [],
    pagination: { page: 1, limit: 50, total: 0, totalPages: 0 },
    currentDate: new Date().toISOString().split('T')[0],
    search: '',
    highlighted: false,
    domainAge: '',
    minRating: '',
    maxRating: '',
    stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
    availableDates: [],
    todayDate: new Date().toISOString().split('T')[0]
  };

  // Update search params function
  const updateSearchParams = React.useCallback((updates: Record<string, string | null>) => {
    const newParams = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      if (value === null || value === "") {
        newParams.delete(key);
      } else {
        newParams.set(key, value);
      }
    });

    setSearchParams(newParams);
  }, [searchParams, setSearchParams]);

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            const searchInput = document.querySelector('input[placeholder*="domain"]') as HTMLInputElement;
            if (searchInput) {
              searchInput.focus();
            }
            break;
          case 'r':
            event.preventDefault();
            window.location.reload();
            break;
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <Header
          title="Domains Expired Tracker"
          subtitle={`Track expired domains with automated hourly updates - Showing ${format(new Date(currentDate), 'MMMM dd, yyyy')}`}
          actions={
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
                className="flex items-center gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            </div>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <StatsCard
            title="Today's Domains"
            value={stats.todayDomains}
            description="Collected today"
            icon={<Globe className="h-4 w-4" />}
          />
          <StatsCard
            title="Highlighted Domains"
            value={stats.highlightedDomains}
            description="Premium domains (≤3 chars)"
            icon={<Star className="h-4 w-4" />}
          />
          <StatsCard
            title="Total Domains"
            value={stats.totalDomains}
            description="In database"
            icon={<Database className="h-4 w-4" />}
          />
          <StatsCard
            title="Filtered Results"
            value={pagination.total}
            description="Current search results"
            icon={<Filter className="h-4 w-4" />}
          />
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Filters & Search
            </CardTitle>
            <CardDescription>
              Refine your domain search and browse by date
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
              {/* Date Selector */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  Date
                </label>
                <Select
                  value={currentDate}
                  onValueChange={(value) => updateSearchParams({ date: value, page: "1" })}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {availableDates.map((date) => (
                      <SelectItem key={date} value={date}>
                        {format(new Date(date), 'MMM dd, yyyy')}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Search */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Search Domains
                </label>
                <EnhancedSearch
                  value={search}
                  onChange={(value) => updateSearchParams({ search: value, page: "1" })}
                  placeholder="Enter domain name..."
                  debounceMs={500}
                />
              </div>

              {/* Highlighted Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium flex items-center gap-2">
                  <Star className="h-4 w-4" />
                  Filter
                </label>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="highlighted"
                    checked={highlighted}
                    onChange={(e) => updateSearchParams({
                      highlighted: e.target.checked ? "true" : null,
                      page: "1"
                    })}
                    className="rounded border-gray-300"
                  />
                  <label htmlFor="highlighted" className="text-sm">
                    Highlighted only (≤3 chars)
                  </label>
                </div>
              </div>

              {/* Domain Age Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Domain Age
                </label>
                <Input
                  type="text"
                  value={domainAge}
                  onChange={(e) => updateSearchParams({ domainAge: e.target.value, page: "1" })}
                  placeholder="e.g., 6 år"
                />
              </div>

              {/* Min Rating Filter */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Min Rating
                </label>
                <Input
                  type="number"
                  value={minRating}
                  onChange={(e) => updateSearchParams({ minRating: e.target.value, page: "1" })}
                  placeholder="0"
                  min="0"
                  max="200"
                />
              </div>

              {/* Clear Filters */}
              <div className="flex items-end">
                <Button
                  variant="outline"
                  onClick={() => updateSearchParams({
                    search: null,
                    highlighted: null,
                    domainAge: null,
                    minRating: null,
                    maxRating: null,
                    page: "1"
                  })}
                >
                  Clear Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Access your most common tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button
                variant="default"
                onClick={() => updateSearchParams({ date: todayDate, page: "1" })}
              >
                View Today's Domains
              </Button>
              <Button
                variant="outline"
                onClick={() => updateSearchParams({
                  date: todayDate,
                  highlighted: "true",
                  page: "1"
                })}
              >
                View Highlighted Domains
              </Button>
              <Form method="post" className="inline">
                <input type="hidden" name="action" value="scrape" />
                <Button
                  variant="secondary"
                  type="submit"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Manual Scrape
                </Button>
              </Form>
            </div>
          </CardContent>
        </Card>

        {/* Domains List */}
        <Card className="overflow-hidden">
          <CardHeader>
            <CardTitle>Domain Results</CardTitle>
            <CardDescription>
              {pagination.total} domains found for {format(new Date(currentDate), 'MMMM dd, yyyy')}
              {search && ` (filtered by "${search}")`}
              {highlighted && " (highlighted only)"}
              {domainAge && ` (age: ${domainAge})`}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="min-w-[200px]">Domæne</TableHead>
                    <TableHead className="min-w-[150px]">Tilføjet</TableHead>
                    <TableHead className="min-w-[100px]">Domænealder</TableHead>
                    <TableHead className="min-w-[80px]">Rating</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {domains.map((domain) => (
                    <TableRow
                      key={domain.id}
                      className={domain.isHighlighted ? "bg-muted/50" : ""}
                    >
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="font-medium">
                            {domain.domainName}
                          </span>
                          {domain.isHighlighted && (
                            <Badge variant="secondary" className="text-xs">
                              <Star className="h-3 w-3 mr-1" />
                              Highlighted
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {format(new Date(domain.createdAt), 'dd.MM.yyyy HH:mm')}
                      </TableCell>
                      <TableCell>
                        {domain.domainAge}
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline">
                          {domain.domainRating}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="px-6 py-4 border-t flex items-center justify-between">
                <div className="flex-1 flex justify-between sm:hidden">
                  {pagination.page > 1 && (
                    <Button
                      variant="outline"
                      onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                    >
                      Previous
                    </Button>
                  )}
                  {pagination.page < pagination.totalPages && (
                    <Button
                      variant="outline"
                      onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                    >
                      Next
                    </Button>
                  )}
                </div>
                <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                  <div>
                    <p className="text-sm text-muted-foreground">
                      Showing{' '}
                      <span className="font-medium">{(pagination.page - 1) * pagination.limit + 1}</span>
                      {' '}to{' '}
                      <span className="font-medium">
                        {Math.min(pagination.page * pagination.limit, pagination.total)}
                      </span>
                      {' '}of{' '}
                      <span className="font-medium">{pagination.total}</span>
                      {' '}results
                    </p>
                  </div>
                  <div className="flex gap-2">
                    {pagination.page > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateSearchParams({ page: String(pagination.page - 1) })}
                      >
                        Previous
                      </Button>
                    )}
                    {pagination.page < pagination.totalPages && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => updateSearchParams({ page: String(pagination.page + 1) })}
                      >
                        Next
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Empty State */}
        {domains.length === 0 && (
          <EmptyState
            icon={<Database className="h-12 w-12" />}
            title="No domains found"
            description="No domains match your current search criteria. Try adjusting your filters or search terms to find what you're looking for."
            action={{
              label: "Clear All Filters",
              onClick: () => updateSearchParams({
                search: null,
                highlighted: null,
                domainAge: null,
                minRating: null,
                maxRating: null,
                page: "1"
              })
            }}
            className="mt-6"
          />
        )}
      </div>
    </div>
  );
}
