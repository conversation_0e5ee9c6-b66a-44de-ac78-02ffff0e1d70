import { type MetaFunction, type LoaderFunctionArgs } from "react-router";
import { useLoaderData, Link, Form } from "react-router";
import { prisma } from "../lib/db.server";
import { format, startOfDay, endOfDay } from "date-fns";
import { <PERSON><PERSON> } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Badge } from "~/components/ui/badge";
import { StatsCard } from "~/components/nordic/stats-card";
import { Header } from "~/components/nordic/header";
import { Database, Globe, Star, Activity, RefreshCw } from "lucide-react";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { StatsCardSkeleton, TableSkeleton } from "~/components/nordic/loading-states";
import { EmptyState } from "~/components/nordic/empty-state";

export const meta: MetaFunction = () => {
  return [
    { title: "Domains Expired - Track Expired Domains" },
    { name: "description", content: "Track and monitor expired domains with automated data collection" },
  ];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);
  const today = new Date();
  const todayStart = startOfDay(today);
  const todayEnd = endOfDay(today);

  try {
    // Get today's domains
    const [todayDomains, highlightedDomains, totalDomains, availableDates] = await Promise.all([
      prisma.domain.count({
        where: {
          createdAt: {
            gte: todayStart,
            lte: todayEnd,
          },
        },
      }),
      prisma.domain.count({
        where: {
          createdAt: {
            gte: todayStart,
            lte: todayEnd,
          },
          isHighlighted: true,
        },
      }),
      prisma.domain.count(),
      prisma.domain.findMany({
        select: {
          createdAt: true,
        },
        distinct: ['createdAt'],
        orderBy: {
          createdAt: 'desc',
        },
        take: 10,
      }),
    ]);

    // Get recent scraping logs
    const recentLogs = await prisma.scrapingLog.findMany({
      orderBy: {
        startTime: 'desc',
      },
      take: 5,
    });

    return Response.json({
      stats: {
        todayDomains,
        highlightedDomains,
        totalDomains,
      },
      availableDates: availableDates.map(d => format(d.createdAt, 'yyyy-MM-dd')),
      recentLogs,
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  } catch (error) {
    console.error("Error loading home data:", error);
    return Response.json({
      stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
      availableDates: [],
      recentLogs: [],
      todayDate: format(today, 'yyyy-MM-dd'),
    });
  }
}

export default function Home() {
  const data = useLoaderData<typeof loader>();
  const { stats, availableDates, recentLogs, todayDate } = data || {
    stats: { todayDomains: 0, highlightedDomains: 0, totalDomains: 0 },
    availableDates: [],
    recentLogs: [],
    todayDate: new Date().toISOString().split('T')[0]
  };

  return (
    <div className="min-h-screen nordic-gradient">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <Header
          title="Domains Expired Tracker"
          subtitle="Track expired domains with automated hourly updates"
          actions={
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.reload()}
              className="flex items-center gap-2"
            >
              <RefreshCw className="h-4 w-4" />
              Refresh
            </Button>
          }
        />

        {/* Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <StatsCard
            title="Today's Domains"
            value={stats.todayDomains}
            description="Collected today"
            icon={<Globe className="h-4 w-4" />}
          />
          <StatsCard
            title="Highlighted Domains"
            value={stats.highlightedDomains}
            description="Premium domains (≤3 chars)"
            icon={<Star className="h-4 w-4" />}
          />
          <StatsCard
            title="Total Domains"
            value={stats.totalDomains}
            description="In database"
            icon={<Database className="h-4 w-4" />}
          />
        </div>

        {/* Quick Actions */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Quick Actions
            </CardTitle>
            <CardDescription>
              Access your most common tasks
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-4">
              <Button variant="nordic" asChild>
                <Link to={`/domains/${todayDate}`}>
                  View Today's Domains
                </Link>
              </Button>
              <Button variant="nordic-outline" asChild>
                <Link to={`/domains/${todayDate}?highlighted=true`}>
                  View Highlighted Domains
                </Link>
              </Button>
              <Form method="post" action="/api/scrape" className="inline">
                <Button
                  variant="secondary"
                  type="submit"
                  className="flex items-center gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  Manual Scrape
                </Button>
              </Form>
            </div>
          </CardContent>
        </Card>

        {/* Available Dates */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>Available Dates</CardTitle>
            <CardDescription>
              Browse domains from previous collection dates
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
              {availableDates.map((date) => (
                <Button
                  key={date}
                  variant="outline"
                  className="h-auto p-3 text-center"
                  asChild
                >
                  <Link to={`/domains/${date}`}>
                    {format(new Date(date), 'MMM dd, yyyy')}
                  </Link>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Scraping Logs */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Scraping Activity</CardTitle>
            <CardDescription>
              Latest automated domain collection attempts
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Start Time</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Domains Found</TableHead>
                  <TableHead>Duration</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentLogs.map((log) => (
                  <TableRow key={log.id}>
                    <TableCell>
                      {format(new Date(log.startTime), 'MMM dd, yyyy HH:mm')}
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant={
                          log.status === 'completed'
                            ? 'nordic'
                            : log.status === 'failed'
                            ? 'destructive'
                            : 'secondary'
                        }
                      >
                        {log.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {log.domainsFound || '-'}
                    </TableCell>
                    <TableCell>
                      {log.endTime
                        ? `${Math.round(
                            (new Date(log.endTime).getTime() - new Date(log.startTime).getTime()) / 1000
                          )}s`
                        : '-'}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
