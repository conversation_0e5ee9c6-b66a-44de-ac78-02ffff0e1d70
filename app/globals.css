@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Nordic Light Theme */
    --background: 248 250 252; /* nordic-ice */
    --foreground: 30 41 59; /* nordic-midnight */
    --card: 255 255 255;
    --card-foreground: 30 41 59;
    --popover: 255 255 255;
    --popover-foreground: 30 41 59;
    --primary: 59 130 246; /* nordic-aurora-blue */
    --primary-foreground: 248 250 252;
    --secondary: 241 245 249; /* nordic-snow */
    --secondary-foreground: 51 65 85; /* nordic-charcoal */
    --muted: 241 245 249;
    --muted-foreground: 100 116 139; /* nordic-stone */
    --accent: 226 232 240; /* nordic-frost */
    --accent-foreground: 30 41 59;
    --destructive: 239 68 68;
    --destructive-foreground: 248 250 252;
    --border: 226 232 240;
    --input: 226 232 240;
    --ring: 59 130 246;
    --radius: 0.75rem;
  }

  .dark {
    /* Nordic Dark Theme */
    --background: 15 23 42; /* darker midnight */
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 59 130 246;
    --primary-foreground: 15 23 42;
    --secondary: 51 65 85;
    --secondary-foreground: 248 250 252;
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --accent: 71 85 105;
    --accent-foreground: 248 250 252;
    --destructive: 239 68 68;
    --destructive-foreground: 248 250 252;
    --border: 71 85 105;
    --input: 71 85 105;
    --ring: 59 130 246;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  /* Nordic specific utilities */
  .nordic-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--background)) 0%, 
      hsl(var(--secondary)) 100%);
  }
  
  .nordic-card {
    @apply bg-card border border-border rounded-lg shadow-sm;
  }
  
  .nordic-glass {
    @apply bg-white/80 backdrop-blur-sm border border-white/20;
  }
  
  .nordic-text-gradient {
    background: linear-gradient(135deg, 
      hsl(var(--primary)) 0%, 
      hsl(var(--accent-foreground)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
