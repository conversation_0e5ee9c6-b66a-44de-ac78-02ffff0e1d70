@import "tailwindcss";

@layer base {
  :root {
    /* Clean White Theme */
    --background: 255 255 255; /* pure white */
    --foreground: 0 0 0; /* pure black */
    --card: 255 255 255;
    --card-foreground: 0 0 0;
    --popover: 255 255 255;
    --popover-foreground: 0 0 0;
    --primary: 0 0 0; /* black primary */
    --primary-foreground: 255 255 255;
    --secondary: 245 245 245; /* light gray */
    --secondary-foreground: 0 0 0;
    --muted: 245 245 245;
    --muted-foreground: 115 115 115;
    --accent: 240 240 240; /* lighter gray */
    --accent-foreground: 0 0 0;
    --destructive: 220 38 38;
    --destructive-foreground: 255 255 255;
    --border: 229 229 229;
    --input: 229 229 229;
    --ring: 0 0 0;
    --radius: 0.5rem;

    /* Simple Color Palette */
    --primary-blue: 59 130 246;
    --primary-green: 34 197 94;
    --primary-red: 239 68 68;
    --primary-yellow: 245 158 11;
    --primary-purple: 168 85 247;
  }

  .dark {
    /* Clean Dark Theme */
    --background: 0 0 0; /* pure black */
    --foreground: 255 255 255; /* pure white */
    --card: 23 23 23; /* very dark gray */
    --card-foreground: 255 255 255;
    --popover: 23 23 23;
    --popover-foreground: 255 255 255;
    --primary: 255 255 255; /* white primary in dark mode */
    --primary-foreground: 0 0 0;
    --secondary: 38 38 38; /* dark gray */
    --secondary-foreground: 255 255 255;
    --muted: 38 38 38;
    --muted-foreground: 163 163 163;
    --accent: 64 64 64; /* medium gray */
    --accent-foreground: 255 255 255;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 64 64 64;
    --input: 64 64 64;
    --ring: 255 255 255;

    /* Simple Dark Color Palette */
    --primary-blue: 96 165 250;
    --primary-green: 74 222 128;
    --primary-red: 248 113 113;
    --primary-yellow: 251 191 36;
    --primary-purple: 196 181 253;
  }
}

@layer base {
  * {
    border-color: hsl(var(--border));
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}


